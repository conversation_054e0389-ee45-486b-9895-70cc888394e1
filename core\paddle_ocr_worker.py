#!/usr/bin/env python3
"""
PaddleOCR识别工作线程模块 - 升级到PP-OCRv5模型
针对机械图纸进行深度优化的OCR识别系统
"""

import sys
import os
import cv2
import numpy as np
import logging
import re
import time
import json  # 导入json模块
from datetime import datetime
from PySide6.QtCore import QObject, QRunnable, Signal
from typing import List, Dict, Tuple, Optional, Any

# 配置日志记录器
logger = logging.getLogger('OCR_Performance')
logger.setLevel(logging.INFO)
# 创建一个文件处理器
ocr_log_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'ocr_performance.log')
file_handler = logging.FileHandler(ocr_log_path, encoding='utf-8')
file_handler.setLevel(logging.INFO)
# 恢复为普通文本格式化器
formatter = logging.Formatter('%(asctime)s - %(message)s')
file_handler.setFormatter(formatter)
# 将处理器添加到日志记录器
logger.addHandler(file_handler)

# 添加PaddleOCR路径 - 使用本地文件
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 设置与infer_tu2.py相同的GPU环境变量
os.environ["FLAGS_allocator_strategy"] = 'auto_growth'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 导入PaddleOCR相关模块
try:
    import paddle
    from paddleocr import PaddleOCR
    PADDLE_AVAILABLE = True

    # 检测GPU支持
    HAS_GPU_SUPPORT = False
    try:
        if paddle.is_compiled_with_cuda():
            gpu_count = paddle.device.cuda.device_count()
            HAS_GPU_SUPPORT = gpu_count > 0
            if HAS_GPU_SUPPORT:
                print(f"✅ 检测到{gpu_count}个可用GPU")
            else:
                print("⚠️ 已编译CUDA支持，但未检测到可用GPU设备")
        else:
            print("⚠️ PaddlePaddle未使用CUDA编译")
    except Exception as e:
        print(f"⚠️ 检测GPU支持时出错: {e}")
        HAS_GPU_SUPPORT = False

    print("✅ PaddleOCR模块导入成功")
except ImportError as e:
    logging.warning(f"PaddleOCR not available: {e}")
    PADDLE_AVAILABLE = False
    HAS_GPU_SUPPORT = False
    print(f"❌ PaddleOCR模块导入失败: {e}")


class PaddleOCRWorkerSignals(QObject):
    """PaddleOCR工作线程信号"""
    finished = Signal(list)
    progress = Signal(int)
    error = Signal(str)


class PaddleOCRWorker(QRunnable):
    """PaddleOCR识别工作线程 - 升级到PP-OCRv5模型"""

    def __init__(self, image_path: str, languages: list = ['ch_sim', 'en'], masked_regions: list = None, force_cpu: bool = False, cpu_threads: int = 8, direct_recognition: bool = False, is_area_ocr: bool = False):
        super().__init__()
        self.image_path = image_path
        self.languages = languages
        self.masked_regions = masked_regions or []
        self.signals = PaddleOCRWorkerSignals()
        self.force_cpu = force_cpu
        self.cpu_threads = cpu_threads
        self.direct_recognition = direct_recognition
        self.is_area_ocr = is_area_ocr
        self.is_vertical_text = False  # 添加竖排文本标志

        # PP-OCRv5模型路径配置 - 使用相对路径
        self.det_model_dir = "models/detection"
        self.rec_model_dir = "models/recognition"

        # 验证模型文件存在
        self._validate_model_files()

        self.ocr_processor = None

    def distance(self, point1, point2):
        """计算两点间距离"""
        point1 = np.array(point1)
        point2 = np.array(point2)
        return np.linalg.norm(point2 - point1)

    def get_bbox_info(self, box):
        """分析文本框几何信息和方向 - 检测垂直文本"""
        info_dict = {}
        if len(box) > 3:
            distances = [
                self.distance(box[0], box[1]),
                self.distance(box[1], box[2]),
                self.distance(box[2], box[3]),
                self.distance(box[3], box[0])
            ]

            short_side = int(min(distances))
            long_side = int(max(distances))

            sorted_by_y = sorted(box, key=lambda x: x[1], reverse=True)
            sorted_lst_down = sorted(sorted_by_y[:2], key=lambda m:m[0])
            sorted_lst_top = sorted(sorted_by_y[2:], key=lambda m:m[0])

            left_down = sorted_lst_down[0]
            right_down = sorted_lst_down[1]
            left_top = sorted_lst_top[0]
            right_top = sorted_lst_top[1]

            ori_pt = [left_top, right_top, right_down, left_down]

            down_diff = right_down[0] - left_down[0]
            
            # 计算边界框尺寸
            box_array = np.array(box)
            x_min, y_min = np.min(box_array, axis=0)
            x_max, y_max = np.max(box_array, axis=0)
            width = x_max - x_min
            height = y_max - y_min
            
            # 计算宽高比
            aspect_ratio = height / max(width, 1)  # 避免除零错误
            
            # 计算面积
            area = width * height
            
            # 改进的垂直文本判断逻辑
            img_vertical = False
            
            # 小尺寸文本（可能是单个字符）的特殊处理
            if area < 1000:  # 小面积文本框
                # 对于小文本框，只有在高宽比非常大时才认为是垂直文本
                img_vertical = aspect_ratio > 1.8  # 更严格的高宽比阈值
            else:
                # 原始判断逻辑，适用于正常大小的文本
                if abs(down_diff - short_side) < 2:
                    img_vertical = True

            info_dict = {
                'bbox_long': long_side,
                'bbox_short': short_side,
                'ori_pt': ori_pt,
                'is_vertical': img_vertical,
                'width': width,
                'height': height,
                'aspect_ratio': aspect_ratio,
                'area': area
            }
        return info_dict

    def rectify_crop(self, img, info):
        """透视校正和裁剪文本区域 - 支持垂直文本旋转"""
        try:
            h, w = img.shape[:2]
            left_top, right_top, right_down, left_down = info['ori_pt']
            if info['is_vertical']:
                crop_h = info['bbox_long']
                crop_w = info['bbox_short']
            else:
                crop_h = info['bbox_short']
                crop_w = info['bbox_long']

            new_ld = left_down
            new_lt = [new_ld[0], new_ld[1] - crop_h]
            new_rt = [new_ld[0] + crop_w, new_ld[1] - crop_h]
            new_rd = [new_ld[0] + crop_w, new_ld[1]]

            pts1 = np.array([left_top, right_top, right_down, left_down], dtype=np.float32)
            pts2 = np.array([new_lt, new_rt, new_rd, new_ld], dtype=np.float32)

            M = cv2.getPerspectiveTransform(pts1, pts2)
            dst = cv2.warpPerspective(img, M, (w, h))

            new_crop = dst[max(new_lt[1] - 10, 0):min(new_ld[1] + 10, h), max(new_lt[0] - 10, 0):min(new_rt[0] + 10, w)]

            # 垂直文本需要旋转90度
            if info['is_vertical']:
                new_crop = cv2.rotate(new_crop, cv2.ROTATE_90_CLOCKWISE)

            return new_crop

        except Exception as e:
            print(f"⚠️ 透视校正失败: {e}")
            # 降级处理：直接使用边界框裁剪
            x_coords = [point[0] for point in info['ori_pt']]
            y_coords = [point[1] for point in info['ori_pt']]
            x_min, x_max = int(min(x_coords)), int(max(x_coords))
            y_min, y_max = int(min(y_coords)), int(max(y_coords))

            crop = img[y_min:y_max, x_min:x_max]
            if info['is_vertical']:
                crop = cv2.rotate(crop, cv2.ROTATE_90_CLOCKWISE)
            return crop

    def _validate_model_files(self):
        """验证PP-OCRv5模型文件是否存在"""
        if not os.path.exists(self.det_model_dir):
            raise FileNotFoundError(f"检测模型目录不存在: {self.det_model_dir}")
        if not os.path.exists(self.rec_model_dir):
            raise FileNotFoundError(f"识别模型目录不存在: {self.rec_model_dir}")

        required_files = ['inference.pdiparams', 'inference.json', 'inference.yml']
        for model_dir, model_type in [(self.det_model_dir, "检测"), (self.rec_model_dir, "识别")]:
            for file_name in required_files:
                file_path = os.path.join(model_dir, file_name)
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"{model_type}模型文件不存在: {file_path}")

        print("✅ PP-OCRv5模型文件验证通过")

    def run(self):
        """执行PaddleOCR识别"""
        if not PADDLE_AVAILABLE:
            self.signals.error.emit("PaddleOCR功能未启用，请安装PaddlePaddle")
            return

        try:
            start_time = time.time()
            start_datetime = datetime.now()
            print(f"\n📋 OCR任务开始时间: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📄 处理文件: {os.path.basename(self.image_path)}")
            # 仅记录处理文件路径
            # logger.info(f"OCR任务开始 - 文件: {self.image_path}")

            os.environ["OMP_NUM_THREADS"] = str(self.cpu_threads)
            os.environ["KMP_AFFINITY"] = "granularity=fine,compact,1,0"

            print("🚀 正在初始化PP-OCRv5模型...")
            print(f"   CPU线程数: {self.cpu_threads}")
            if self.force_cpu:
                print("   模式: 强制CPU模式")
            else:
                print("   模式: 自动选择GPU/CPU")

            self.signals.progress.emit(10)
            print("🔧 正在初始化PP-OCRv5模型...")
            init_start = time.time()

            # 初始化PP-OCRv5模型
            # 使用与improved_large_image_ocr.py完全相同的参数配置
            self.ocr_processor = PaddleOCR(
                text_detection_model_dir=self.det_model_dir,
                text_recognition_model_dir=self.rec_model_dir,
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )

            init_time = time.time() - init_start
            print(f"✅ PP-OCRv5模型初始化完成，耗时: {init_time:.2f}秒")
            print(f"✅ CPU线程数: {self.cpu_threads}")
            # logger.info(f"模型初始化耗时: {init_time:.2f}秒")
            self.signals.progress.emit(30)
            
            print(f"📖 正在处理文件: {self.image_path}")
            image_start = time.time()
            
            image = cv2.imread(self.image_path)
            if image is None:
                raise Exception(f"无法读取图像文件: {self.image_path}")
            
            image_time = time.time() - image_start
            img_height, img_width = image.shape[:2]
            img_size_mb = (image.nbytes / (1024 * 1024))

            # 根据模式设置最大检测框面积阈值
            if self.is_area_ocr:
                # 区域OCR模式下，禁用大面积过滤
                max_box_area = float('inf')
                print("📐 区域OCR模式: 已禁用最大检测框面积过滤。")
            else:
                # 全局OCR模式，使用基于图像面积的阈值
                max_box_area = (img_height * img_width) / 5
                print(f"🖼️ 全局OCR模式: 已设置最大检测框面积阈值: {max_box_area:.0f} (基于图像面积的1/5)")

            
            print(f"🖼️ 图像读取成功，尺寸: {image.shape}，耗时: {image_time:.2f}秒")
            # logger.info(f"图像尺寸: {img_width}x{img_height}，大小: {img_size_mb:.2f}MB，读取耗时: {image_time:.2f}秒")
            self.signals.progress.emit(50)
            
            print("🔍 开始OCR识别...")
            ocr_start = time.time()

            # 使用PP-OCRv5进行OCR识别
            print("📄 使用PP-OCRv5完整OCR流程处理（检测+识别）")
            ocr_results = self._process_with_ppocr_v5(image, max_box_area)

            ocr_time = time.time() - ocr_start
            print(f"✅ 识别完成，共识别 {len(ocr_results)} 个文本，OCR处理耗时: {ocr_time:.2f}秒")
            # logger.info(f"OCR处理完成，识别到 {len(ocr_results)} 个文本，处理耗时: {ocr_time:.2f}秒")
            self.signals.progress.emit(90)
            
            post_start = time.time()
            final_results = self._format_results_for_pyqt(ocr_results, image.shape)
            post_time = time.time() - post_start
            print(f"✅ 后处理完成，耗时: {post_time:.2f}秒")
            
            total_time = time.time() - start_time
            end_datetime = datetime.now()
            
            print("\n⏱️ 时间统计:")
            print(f"  模型初始化: {init_time:.2f}秒 ({(init_time/total_time*100):.1f}%)")
            print(f"  图像读取: {image_time:.2f}秒 ({(image_time/total_time*100):.1f}%)")
            print(f"  OCR处理: {ocr_time:.2f}秒 ({(ocr_time/total_time*100):.1f}%)")
            print(f"  后处理: {post_time:.2f}秒 ({(post_time/total_time*100):.1f}%)")
            print(f"  总耗时: {total_time:.2f}秒")
            print(f"📋 OCR任务结束时间: {end_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 按照用户要求，记录普通文本格式的日志
            summary_line = (
                f"文件: {os.path.basename(self.image_path)}, "
                f"尺寸: {img_width}x{img_height}, "
                f"OCR识别时间: {ocr_time:.2f}秒, "
                f"识别结果数: {len(final_results)}"
            )
            logger.info(summary_line)
            
            # 记录每个识别出的结果
            for i, result in enumerate(final_results):
                result_line = (
                    f"  [{i+1:03d}] 文本: '{result.get('text', '')}', "
                    f"置信度: {result.get('confidence', 0.0):.3f}, "
                    f"类型: {result.get('type', 'unknown')}"
                )
                
                # 如果文本经过了垂直校正，添加额外信息
                original_text = result.get('original_text_before_rectify')
                if original_text:
                    result_line += f" (垂直校正自: '{original_text}')"
                
                logger.info(result_line)

            # 添加一个分隔符，便于区分不同文件的日志
            logger.info("-" * 80)
            
            self.signals.progress.emit(100)
            self.signals.finished.emit(final_results)
            
        except Exception as e:
            error_msg = f"PaddleOCR识别失败: {str(e)}"
            print(f"❌ {error_msg}")
            
            # 记录普通文本格式的错误日志
            image_name = os.path.basename(self.image_path) if self.image_path else "Unknown"
            logger.error(f"文件: {image_name}, OCR识别失败: {str(e)}")
            logger.error("-" * 80)

            import traceback
            traceback.print_exc()
            self.signals.error.emit(error_msg)
            
    def _process_with_ppocr_v5(self, image, max_box_area=float('inf')):
        """使用PP-OCRv5进行OCR识别"""
        try:
            # 使用PP-OCRv5进行OCR识别 - 使用与improved_large_image_ocr.py相同的方法
            ocr_result = self.ocr_processor.predict(image)

            if not ocr_result:
                return []

            results = []
            img_h, img_w = image.shape[:2]
            # 只有在全局OCR时才计算最大面积阈值，区域OCR则不限制
            max_box_area = float('inf')
            if not self.is_area_ocr:
                max_box_area = (img_h * img_w) / 5
                print(f"🖼️ 全局OCR模式: 已设置最大检测框面积阈值: {max_box_area:.0f} (基于图像面积的1/5)")
            else:
                print("📐 区域OCR模式: 已禁用最大检测框面积过滤。")

            # 处理predict()方法返回的结果格式
            for result in ocr_result:
                result_data = result.json if hasattr(result, 'json') else result
                if isinstance(result_data, dict) and 'res' in result_data:
                    ocr_res = result_data['res']
                    dt_polys = ocr_res.get('dt_polys', [])
                    rec_texts = ocr_res.get('rec_texts', [])
                    rec_scores = ocr_res.get('rec_scores', [])

                    # 处理每个检测到的文本
                    for i, (poly, text, confidence) in enumerate(zip(dt_polys, rec_texts, rec_scores)):
                        if poly is not None and text and confidence >= 0.3:
                            # 转换边界框格式
                            bbox = poly.tolist() if hasattr(poly, 'tolist') else poly

                            # 计算边界框面积，过滤异常大的框
                            if len(bbox) >= 4:
                                box_array = np.array(bbox, dtype=np.float32)
                                x_min, y_min = np.min(box_array, axis=0)
                                x_max, y_max = np.max(box_array, axis=0)
                                box_area = (x_max - x_min) * (y_max - y_min)

                                if box_area > max_box_area:
                                    # 仅在全局OCR时打印此警告
                                    if not self.is_area_ocr:
                                        print(f"⚠️ 过滤掉一个异常大的检测框，面积: {box_area:.0f} > 阈值: {max_box_area:.0f}")
                                    continue

                                # 分析文本框几何信息，检测垂直文本
                                bbox_info = self.get_bbox_info(bbox)
                                is_vertical = bbox_info.get('is_vertical', False)

                                # 单字符特殊处理：除非几何特征非常明确，否则将单个字符视为水平文本
                                if len(text.strip()) == 1:
                                    # 只有当高宽比非常大(>2.0)时才认为单个字符是垂直的
                                    aspect_ratio = bbox_info.get('aspect_ratio', 1.0)
                                    if aspect_ratio < 2.0:
                                        # 强制将单字符标记为水平文本
                                        is_vertical = False
                                        if bbox_info.get('is_vertical', False):
                                            print(f"🔄 单字符 '{text}' 被重置为水平文本 (原高宽比: {aspect_ratio:.2f})")
                                # 两个字符特殊处理：同样使用更严格的标准判断垂直性
                                elif len(text.strip()) == 2:
                                    # 对于两个字符的文本，提高判断垂直文本的高宽比阈值为2.2
                                    aspect_ratio = bbox_info.get('aspect_ratio', 1.0)
                                    if aspect_ratio < 2.2:
                                        # 强制将两字符标记为水平文本
                                        is_vertical = False
                                        if bbox_info.get('is_vertical', False):
                                            print(f"🔄 两字符文本 '{text}' 被重置为水平文本 (原高宽比: {aspect_ratio:.2f})")

                                final_text = text
                                final_confidence = confidence
                                original_text_before_rectify = None

                                # 如果检测到垂直文本，进行透视校正和重新识别
                                if is_vertical and bbox_info:
                                    try:
                                        print(f"🔄 检测到垂直文本: '{text}', 进行透视校正...")

                                        # 透视校正和裁剪
                                        crop_img = self.rectify_crop(image, bbox_info)

                                        if crop_img is not None and crop_img.size > 0:
                                            # 对校正后的图像重新进行OCR识别
                                            rectified_result = self.ocr_processor.predict(crop_img)

                                            if rectified_result:
                                                for rect_res in rectified_result:
                                                    rect_data = rect_res.json if hasattr(rect_res, 'json') else rect_res
                                                    if isinstance(rect_data, dict) and 'res' in rect_data:
                                                        rect_ocr_res = rect_data['res']
                                                        rect_texts = rect_ocr_res.get('rec_texts', [])
                                                        rect_scores = rect_ocr_res.get('rec_scores', [])

                                                        if rect_texts and rect_scores:
                                                            # 使用校正后的识别结果
                                                            original_text_before_rectify = text
                                                            final_text = rect_texts[0]
                                                            final_confidence = rect_scores[0]
                                                            print(f"✅ 垂直文本校正成功: '{text}' → '{final_text}' (置信度: {final_confidence:.3f})")
                                                            break

                                    except Exception as e:
                                        print(f"⚠️ 垂直文本校正失败: {e}, 使用原始识别结果")

                                result_item = {
                                    'text': final_text,
                                    'confidence': final_confidence,
                                    'bbox': bbox,
                                    'text_type': self.classify_text_type(final_text),
                                    'is_vertical': is_vertical,
                                }
                                if original_text_before_rectify:
                                    result_item['original_text_before_rectify'] = original_text_before_rectify
                                
                                results.append(result_item)

            return results

        except Exception as e:
            print(f"❌ PP-OCRv5处理失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def classify_text_type(self, text: str) -> str:
        """对识别的文本进行类型分类 - 兼容PP-OCRv5"""
        return self._classify_mechanical_text(text)



    def _format_results_for_pyqt(self, ocr_results: List[Dict], image_shape: Tuple[int, int, int]) -> List[Dict]:
        """将OCR结果格式化为PyQt应用程序所需的格式"""
        formatted_results = []
        total_results = len(ocr_results)
        masked_count = 0
        
        for result in ocr_results:
            bbox = result.get('bbox')
            text = result.get('text', '')
            
            if not bbox or len(bbox) < 4:
                continue

            bbox_array = np.array(bbox)
            center_x = int(np.mean(bbox_array[:, 0]))
            center_y = int(np.mean(bbox_array[:, 1]))
            bbox_width = int(np.max(bbox_array[:, 0]) - np.min(bbox_array[:, 0]))
            bbox_height = int(np.max(bbox_array[:, 1]) - np.min(bbox_array[:, 1]))
            
            if self.masked_regions and self._is_bbox_in_masked_region(bbox):
                masked_count += 1
                continue
                
            clean_text = self._clean_text(text)
            if not clean_text:
                continue
            
            # 使用PP-OCRv5处理结果中的text_type，如果没有则重新分类
            text_type = result.get('text_type', self._classify_mechanical_text(clean_text))
            is_vertical = result.get('is_vertical', False)

            formatted_results.append({
                'text': clean_text,
                'confidence': result.get('confidence', 0.0),
                'center_x': center_x,
                'center_y': center_y,
                'bbox_width': bbox_width,
                'bbox_height': bbox_height,
                'bbox': bbox,
                'type': text_type,
                'original_text': text,
                'is_vertical': is_vertical,
                'original_text_before_rectify': result.get('original_text_before_rectify')
            })
        
        if masked_count > 0:
            print(f"🚫 已根据屏蔽区域过滤掉 {masked_count}/{total_results} 个结果。")

        # 统计垂直文本
        vertical_count = sum(1 for result in formatted_results if result.get('is_vertical', False))
        if vertical_count > 0:
            print(f"🔄 检测到 {vertical_count} 个垂直文本框，已进行透视校正")

        return formatted_results

    def _is_bbox_in_masked_region(self, bbox: List[Tuple[int, int]]) -> bool:
        """检查给定的边界框是否完全位于任何一个屏蔽区域内"""
        bbox_center = np.mean(np.array(bbox), axis=0)
        
        for region in self.masked_regions:
            if (region['x'] <= bbox_center[0] <= region['x'] + region['width'] and
                region['y'] <= bbox_center[1] <= region['y'] + region['height']):
                return True
        return False

    def _clean_text(self, text: str) -> str:
        """清理OCR识别出的原始文本"""
        return text.strip()
    
    def _classify_mechanical_text(self, text: str) -> str:
        """根据文本内容对机械图纸中的文本进行分类"""
        # 保存原始文本，便于调试
        original_text = text
        
        # 标准化处理
        text = text.upper().replace(" ", "")
        
        # 调试输出
        print(f"🔍 分类文本: '{original_text}' -> '{text}'")
        
        # --- 工程符号识别 ---
        # 匹配图示中的各种特殊符号
        engineering_symbols = [
            '—', '-', '_', '□', '○', '⊥', '∥', '⊾', '△', '◇', 'Ø', 'ø', 
            '⌀', '∅', '±', '√', '∇', '≡', '→', '⟷', '⊙', '[A]', '[Z]', 
            '(M)', '(S)', '(P)', '(F)', '(R)', '(L)', '(T)', '(U)', 'SØ', 
            '↓', '\\/', '\\/', '↗', '↖', '↘', '↙'  # 新增各方向箭头符号
        ]
        
        # 检查是否包含箭头符号（优先处理）
        arrow_symbols = ['→', '↓', '↑', '←', '↗', '↖', '↘', '↙', '⟶', '⟵', '⟷']
        if any(arrow in original_text for arrow in arrow_symbols):
            print(f"✅ 识别为带箭头的符号: {original_text}")
            return "symbol"
            
        # 检查是否完全匹配任一工程符号
        if original_text.strip() in engineering_symbols:
            print(f"✅ 识别为工程符号: {original_text}")
            return "symbol"
            
        # 部分符号需要特殊处理（包含这些符号的文本）
        if any(sym in original_text for sym in ['□', '○', '△', '◇', '⊥', '∥', '⊙', '∇', '≡', '→', '↓', '⟷']):
            print(f"✅ 识别为包含工程符号的文本: {original_text}")
            return "symbol"
            
        # 单独处理横线和连字符，避免误判
        if original_text.strip() in ['—', '-', '_'] and len(original_text.strip()) == 1:
            print(f"✅ 识别为线型符号: {original_text}")
            return "symbol"
        
        # 新增：公差优先判断 (最高优先级)
        # 原始文本带空格检查，以避免意外匹配
        stripped_original = original_text.strip()
        if stripped_original.startswith(('±', '+', '-')):
            # 确保不是只有一个'-'且后面不是数字，以排除分隔线
            if stripped_original == '-' or (stripped_original.startswith('-') and len(stripped_original) > 1 and not stripped_original[1].isdigit()):
                pass # 这种情况可能是分隔线，继续往下判断
            else:
                print(f"✅ 识别为公差: {original_text}")
                return "tolerance"

        # 螺纹规格匹配 - 例如 M10, M8x1.25
        if re.match(r'^M\d+(\.\d+)?(X\d+(\.\d+)?)?', text):
            print(f"✅ 识别为螺纹规格: {text}")
            return "thread_spec"  # 使用英文类型名
            
        # 直径标注匹配 - 扩展匹配模式
        # 1. 标准格式：Φ10, ∅20, Ø30 等
        # 2. 反向格式：80Ø, 68Ø 等
        # 3. 复合格式：P.C.D.Ø80 等
        if (re.match(r'^(Φ|∅|Ø|⌀)\d+', text) or  # 标准格式
            re.search(r'\d+(Φ|∅|Ø|⌀)', text) or   # 反向格式
            'Ø' in original_text or '∅' in original_text or '⌀' in original_text or
            re.search(r'P\.?C\.?D\.?(Φ|∅|Ø|⌀)\d+', text)):  # 复合格式
            print(f"✅ 识别为直径标注: {text}")
            return "diameter"  # 使用英文类型名
            
        # 角度标注匹配 - 例如 30°, 45.5°
        if '°' in original_text or '度' in original_text or re.match(r'^\d+(\.\d+)?°?$', original_text) and int(float(re.match(r'^\d+(\.\d+)?°?$', original_text).group().replace('°', ''))) in [10, 15, 30, 45, 60, 75, 90]:
            print(f"✅ 识别为角度标注: {text}")
            return "angle"  # 使用英文类型名
        
        # 材料标记匹配 - 例如 Q235, 45#, GCr15 (优先于尺寸标注)
        if (re.match(r'^[A-Z][A-Z0-9]{2,}$', text) or  # Q235, GCr15等
            re.match(r'^[A-Z]-\d+$', text) or          # A-1等
            re.match(r'^\d+#$', text)):                # 45#等
            print(f"✅ 识别为材料标记: {text}")
            return "material"  # 使用英文类型名

        # 尺寸标注匹配 (更宽松的规则)
        # 1. 包含数字
        # 2. 可能包含小数点、加减符号、公差等
        # 3. 可能是纯数字 如 "54" "0.01" "±0.1" "45±1"
        if re.search(r'\d', text):
            print(f"✅ 识别为尺寸标注: {text}")
            return "dimension"  # 使用英文类型名
        
        # 默认为普通标注
        print(f"⚠️ 未能明确分类，设为普通标注: {text}")
        return 'annotation'
