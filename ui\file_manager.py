#!/usr/bin/env python3
"""
文件操作管理模块 - 处理文件加载、PDF处理、导出功能
"""

import os
import time
import logging
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QMessageBox, QInputDialog, QProgressBar, QApplication
)
from PySide6.QtCore import Qt, QObject, Signal, QRectF
from PySide6.QtGui import QPixmap, QAction

from core.workers import FileLoaderWorker, PDFLoaderWorker
from core.file_loader import FileLoader
from utils.constants import (
    FILE_DIALOG_FILTER, SUPPORTED_IMAGE_FORMATS, SUPPORTED_PDF_FORMATS, 
    SUPPORTED_DXF_FORMATS, PDF_QUALITY_OPTIONS
)
from utils.dependencies import HAS_OCR_SUPPORT, HAS_EXCEL_SUPPORT

# 获取已配置的logger
logger = logging.getLogger('PyQtBubble.FileManager')

class FileManager(QObject):
    """文件管理器 - 处理所有文件操作相关功能"""
    
    # 信号定义
    file_loaded = Signal(str, QPixmap)  # 文件加载完成信号
    pdf_file_loaded = Signal(str, int)  # PDF文件加载完成信号
    file_load_error = Signal(str)  # 文件加载错误信号
    file_load_progress = Signal(int, str)  # 文件加载进度信号
    pdf_page_loaded = Signal(QPixmap, str)  # PDF页面加载完成信号
    pdf_load_error = Signal(str)  # PDF加载错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        
        # 文件相关属性
        self.current_file_path = ""
        self.current_pixmap = None
        
        # PDF相关属性
        self.pdf_file_path = ""
        self.pdf_page_count = 0
        self.current_pdf_page = 0
        self.previous_page = 0
        self.pdf_pages_cache = {}  # 页面缓存：{页码: 临时文件路径}
        
        # 多页文档的数据存储
        self.annotations_by_page = {}  # {页码: 标注数据列表}
        self.ocr_results_by_page = {}  # {页码: OCR结果列表}
        
        # PDF导航控件（将在setup_pdf_navigation_controls中创建）
        self.pdf_nav_widget = None
        self.prev_page_btn = None
        self.next_page_btn = None
        self.page_label = None
        self.go_to_page_btn = None
        
    def setup_pdf_navigation_controls(self, status_bar):
        """设置PDF导航控件（放在状态栏右侧）"""
        from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QLabel
        
        # 创建一个小部件来容纳导航控件
        self.pdf_nav_widget = QWidget()
        self.pdf_nav_layout = QHBoxLayout(self.pdf_nav_widget)
        self.pdf_nav_layout.setContentsMargins(0, 0, 5, 0)
        self.pdf_nav_layout.setSpacing(5)
        
        # 创建导航按钮和标签
        self.prev_page_btn = QPushButton("◀ 上一页")
        self.prev_page_btn.setMaximumWidth(80)
        self.prev_page_btn.setToolTip("显示上一页 (快捷键: 左方向键)")
        self.prev_page_btn.setEnabled(False)
        self.prev_page_btn.clicked.connect(self.go_to_prev_page)
        self.pdf_nav_layout.addWidget(self.prev_page_btn)
        
        self.page_label = QLabel("页码: 0 / 0")
        self.page_label.setFixedWidth(80)
        self.page_label.setAlignment(Qt.AlignCenter)
        self.pdf_nav_layout.addWidget(self.page_label)
        
        self.next_page_btn = QPushButton("下一页 ▶")
        self.next_page_btn.setMaximumWidth(80)
        self.next_page_btn.setToolTip("显示下一页 (快捷键: 右方向键)")
        self.next_page_btn.setEnabled(False)
        self.next_page_btn.clicked.connect(self.go_to_next_page)
        self.pdf_nav_layout.addWidget(self.next_page_btn)
        
        self.go_to_page_btn = QPushButton("前往...")
        self.go_to_page_btn.setMaximumWidth(60)
        self.go_to_page_btn.setToolTip("跳转到指定页面")
        self.go_to_page_btn.setEnabled(False)
        self.go_to_page_btn.clicked.connect(self.show_go_to_page_dialog)
        self.pdf_nav_layout.addWidget(self.go_to_page_btn)
        
        # 将导航小部件添加到状态栏右侧
        status_bar.addPermanentWidget(self.pdf_nav_widget)
        
        # 默认隐藏导航控件
        self.pdf_nav_widget.setVisible(False)
        
    def open_file(self):
        """打开文件对话框选择文件"""
        # 创建文件对话框并设置过滤器
        file_dialog = QFileDialog(self.parent)
        file_dialog.setWindowTitle("打开文件")
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter(FILE_DIALOG_FILTER)
        
        # 设置默认目录
        if self.current_file_path:
            dir_path = str(Path(self.current_file_path).parent)
            file_dialog.setDirectory(dir_path)
        
        # 显示对话框
        if file_dialog.exec():
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                if hasattr(self.parent, 'status_bar'):
                    self.parent.status_bar.showMessage(f"正在加载文件: {file_paths[0]}...")
                self.load_file(file_paths[0])

    def load_file(self, file_path: str):
        """加载文件
        
        Args:
            file_path: 文件路径
        """
        if not file_path or not Path(file_path).exists():
            QMessageBox.warning(self.parent, "错误", f"文件不存在: {file_path}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.clearMessage()
            return
            
        # 清除当前选中的标注
        if hasattr(self.parent, 'current_annotation') and self.parent.current_annotation:
            self.parent.current_annotation = None
            if hasattr(self.parent, 'property_editor'):
                self.parent.property_editor.set_annotation(None, None, None)
        
        # 转换为Path对象以便于处理
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        # 确保扩展名有效
        if extension not in SUPPORTED_IMAGE_FORMATS + SUPPORTED_PDF_FORMATS + SUPPORTED_DXF_FORMATS:
            QMessageBox.warning(self.parent, "错误", f"不支持的文件格式: {extension}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.clearMessage()
            return
        
        # 显示加载对话框
        if hasattr(self.parent, 'loading_dialog'):
            self.parent.loading_label.setText(f"⏳ 正在加载文件...\n{file_path.name}")
            self.parent.loading_dialog.resize(self.parent.size())
            self.parent.loading_dialog.show()
            QApplication.processEvents()  # 确保UI立即更新
        
        # 清除当前状态
        self.clear_current_state()
        
        # 创建并启动文件加载线程
        pdf_quality = "高清 (4x)"
        if hasattr(self.parent, 'pdf_quality_combo'):
            pdf_quality = self.parent.pdf_quality_combo.currentText()
            
        file_loader = FileLoaderWorker(str(file_path), pdf_quality=pdf_quality)
        
        # 连接信号
        file_loader.signals.progress.connect(self._on_file_load_progress)
        file_loader.signals.finished.connect(self._on_file_loaded)
        file_loader.signals.pdf_loaded.connect(self._on_pdf_file_loaded)
        file_loader.signals.error.connect(self._on_file_load_error)
        
        # 启动线程
        if hasattr(self.parent, 'thread_pool'):
            self.parent.thread_pool.start(file_loader)

    def clear_current_state(self):
        """清除当前状态"""
        # 清除标注和OCR结果
        if hasattr(self.parent, 'clear_annotations'):
            self.parent.clear_annotations(show_empty_message=False)
        if hasattr(self.parent, 'annotation_table'):
            self.parent.annotation_table.clear_annotations()
        if hasattr(self.parent, 'ocr_manager'):
            self.parent.ocr_manager.clear_ocr_results()
        if hasattr(self.parent, 'graphics_scene'):
            self.parent.graphics_scene.clear()
        if hasattr(self.parent, 'annotation_counter'):
            self.parent.annotation_counter = 0
        
        # 重置PDF相关状态
        self.pdf_file_path = None
        self.pdf_page_count = 0
        self.current_pdf_page = 0
        self.pdf_pages_cache.clear()
        self.annotations_by_page.clear()
        self.ocr_results_by_page.clear()
        
        # 隐藏PDF导航控件
        if self.pdf_nav_widget:
            self.pdf_nav_widget.setVisible(False)

    def _on_file_load_progress(self, progress: int, message: str):
        """文件加载进度更新"""
        if hasattr(self.parent, 'loading_label'):
            self.parent.loading_label.setText(f"⏳ {message}\n({progress}%)")
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(message)
        QApplication.processEvents()
        self.file_load_progress.emit(progress, message)

    def _on_file_loaded(self, file_path: str, pixmap: QPixmap):
        """文件加载完成回调"""
        try:
            self.current_file_path = file_path
            self.current_pixmap = pixmap
            
            # 清除当前场景
            if hasattr(self.parent, 'graphics_scene'):
                self.parent.graphics_scene.clear()
                # 添加图像到场景
                self.parent.graphics_scene.addPixmap(pixmap)
                logger.debug(f"图像已添加到场景，尺寸: {pixmap.width()}x{pixmap.height()}")
            
            # 初始化标注ID计数器
            if hasattr(self.parent, 'annotation_counter'):
                self.parent.annotation_counter = 0
            
            # 执行一次居中操作
            if hasattr(self.parent, 'center_view'):
                self.parent.center_view()
            
            # 更新状态栏
            file_name = Path(file_path).name
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(
                    f"已加载: {file_name} ({pixmap.width()}x{pixmap.height()})", 5000
                )
            
            # 隐藏加载对话框
            if hasattr(self.parent, 'loading_dialog'):
                self.parent.loading_dialog.hide()
                
            # 发出信号
            self.file_loaded.emit(file_path, pixmap)
            
        except Exception as e:
            self._on_file_load_error(f"处理加载结果时出错: {str(e)}")
            logger.exception("图像加载完成处理错误")

    def _on_pdf_file_loaded(self, file_path: str, page_count: int):
        """PDF文件加载完成"""
        try:
            # 设置PDF相关属性
            self.pdf_file_path = file_path
            self.pdf_page_count = page_count
            self.current_pdf_page = 0  # 从第一页开始
            
            # 如果是多页PDF，显示导航控件
            if page_count > 1:
                self.update_pdf_navigation_controls()
                
                # 在状态栏显示提示消息
                if hasattr(self.parent, 'status_bar'):
                    self.parent.status_bar.showMessage(
                        f"多页PDF文件，共 {page_count} 页，使用右下角导航控件或键盘方向键切换页面", 5000
                    )
            
            # 加载第一页
            self.load_pdf_page(0)
            
            # 发出信号
            self.pdf_file_loaded.emit(file_path, page_count)
            
        except Exception as e:
            self._on_file_load_error(f"处理PDF文件时出错: {str(e)}")

    def _on_file_load_error(self, error_msg: str):
        """文件加载错误处理"""
        QMessageBox.warning(self.parent, "加载错误", error_msg)
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(f"❌ 文件加载失败: {error_msg}", 5000)
        if hasattr(self.parent, 'loading_dialog'):
            self.parent.loading_dialog.hide()
        self.file_load_error.emit(error_msg)

    def load_pdf_page(self, page_index: int, skip_save: bool = False):
        """加载指定页码的PDF页面"""
        if not self.pdf_file_path or page_index < 0 or page_index >= self.pdf_page_count:
            return False
            
        # 保存当前页数据（如果需要）
        if not skip_save and hasattr(self.parent, 'save_current_page_data'):
            self.parent.save_current_page_data()
        
        # 更新页面索引
        self.previous_page = self.current_pdf_page
        self.current_pdf_page = page_index
        
        # 检查缓存
        temp_path = self.pdf_pages_cache.get(page_index)
        if temp_path and os.path.exists(temp_path):
            # 从缓存加载
            self._load_from_cache(temp_path, page_index)
        else:
            # 创建新的PDF加载线程
            self._load_pdf_page_async(page_index)
            
        return True
        
    def _load_from_cache(self, temp_path: str, page_index: int):
        """从缓存加载页面"""
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(f"从缓存加载PDF页面 {page_index+1}/{self.pdf_page_count}...")
        if hasattr(self.parent, 'loading_label'):
            self.parent.loading_label.setText(f"正在从缓存加载第 {page_index+1}/{self.pdf_page_count} 页...\n请稍候")
        QApplication.processEvents()
        
        # 在后台线程中加载缓存图像
        def load_cached_image():
            try:
                cache_start = time.time()
                logger.debug(f"开始从缓存加载图像...")
                pixmap = QPixmap(temp_path)
                if not pixmap.isNull():
                    logger.debug(f"缓存图像加载成功，耗时: {time.time() - cache_start:.2f}秒")
                    # 发出信号
                    self.pdf_page_loaded.emit(pixmap, temp_path)
                else:
                    logger.error("缓存图像加载失败")
                    self.pdf_load_error.emit("缓存图像加载失败")
            except Exception as e:
                logger.exception(f"从缓存加载图像时出错: {str(e)}")
                self.pdf_load_error.emit(f"从缓存加载图像时出错: {str(e)}")
        
        # 使用线程池执行缓存加载
        from PySide6.QtCore import QRunnable
        class CacheLoader(QRunnable):
            def __init__(self, func):
                super().__init__()
                self.func = func
            def run(self):
                self.func()
        
        if hasattr(self.parent, 'thread_pool'):
            self.parent.thread_pool.start(CacheLoader(load_cached_image))
        
    def _load_pdf_page_async(self, page_index: int):
        """异步加载PDF页面"""
        # 显示加载对话框
        if hasattr(self.parent, 'loading_dialog'):
            self.parent.loading_label.setText(f"正在加载第 {page_index+1}/{self.pdf_page_count} 页...\n请稍候")
            self.parent.loading_dialog.resize(self.parent.size())
            self.parent.loading_dialog.show()
            QApplication.processEvents()
        
        # 获取PDF质量设置
        quality = 4.0
        force_resolution = False
        if hasattr(self.parent, 'pdf_quality_combo'):
            quality_text = self.parent.pdf_quality_combo.currentText()
            quality = PDF_QUALITY_OPTIONS.get(quality_text, 4.0)
        if hasattr(self.parent, 'force_resolution_checkbox'):
            force_resolution = self.parent.force_resolution_checkbox.isChecked()
        
        # 创建PDF加载工作器
        pdf_loader = PDFLoaderWorker(
            self.pdf_file_path, page_index, quality, force_resolution
        )
        
        # 连接信号
        pdf_loader.signals.finished.connect(self._on_pdf_loaded)
        pdf_loader.signals.error.connect(self._on_pdf_load_error)
        pdf_loader.signals.progress.connect(lambda p: None)  # 忽略进度信号
        
        # 启动线程
        if hasattr(self.parent, 'thread_pool'):
            self.parent.thread_pool.start(pdf_loader)

    def _on_pdf_loaded(self, pixmap: QPixmap, temp_path: str):
        """PDF加载完成处理"""
        try:
            # 缓存页面
            self.pdf_pages_cache[self.current_pdf_page] = temp_path
            
            # 更新当前图像
            self.current_pixmap = pixmap
            
            # 清除场景并添加新图像
            if hasattr(self.parent, 'graphics_scene'):
                self.parent.graphics_scene.clear()
                self.parent.graphics_scene.addPixmap(pixmap)
            
            # 恢复页面数据
            if hasattr(self.parent, 'restore_page_data'):
                self.parent.restore_page_data(self.current_pdf_page)
            
            # 更新导航控件
            self.update_pdf_navigation_controls()
            
            # 居中显示
            if hasattr(self.parent, 'center_view'):
                self.parent.center_view()
            
            # 更新状态栏
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(
                    f"已加载PDF第 {self.current_pdf_page+1}/{self.pdf_page_count} 页", 3000
                )
            
            # 隐藏加载对话框
            if hasattr(self.parent, 'loading_dialog'):
                self.parent.loading_dialog.hide()
                
            # 发出信号
            self.pdf_page_loaded.emit(pixmap, temp_path)
            
        except Exception as e:
            logger.exception(f"PDF加载完成处理中发生异常: {str(e)}")
            self._on_pdf_load_error(f"处理加载结果时出错: {str(e)}")

    def _on_pdf_load_error(self, error_msg: str):
        """PDF加载出错处理"""
        logger.error(f"PDF加载错误: {error_msg}")
        # 恢复到之前的页面
        self.current_pdf_page = self.previous_page
        self.update_pdf_navigation_controls()
        
        QMessageBox.warning(self.parent, "页面加载失败", error_msg)
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(f"❌ 页面加载失败: {error_msg}", 3000)
        
        # 隐藏加载对话框
        if hasattr(self.parent, 'loading_dialog'):
            self.parent.loading_dialog.hide()
            
        self.pdf_load_error.emit(error_msg)

    def update_pdf_navigation_controls(self):
        """更新PDF导航控件的状态"""
        if not self.pdf_file_path:
            if self.pdf_nav_widget:
                self.pdf_nav_widget.setVisible(False)
            return
            
        # 显示导航控件
        if self.pdf_nav_widget:
            self.pdf_nav_widget.setVisible(True)
            
        # 更新页码显示
        if self.page_label:
            self.page_label.setText(f"页码: {self.current_pdf_page+1} / {self.pdf_page_count}")
        
        # 更新导航按钮状态
        if self.prev_page_btn:
            self.prev_page_btn.setEnabled(self.current_pdf_page > 0)
        if self.next_page_btn:
            self.next_page_btn.setEnabled(self.current_pdf_page < self.pdf_page_count - 1)
        if self.go_to_page_btn:
            self.go_to_page_btn.setEnabled(self.pdf_page_count > 1)

    def go_to_prev_page(self):
        """转到上一页"""
        if self.pdf_file_path and self.current_pdf_page > 0:
            self.load_pdf_page(self.current_pdf_page - 1)
            
    def go_to_next_page(self):
        """转到下一页"""
        if self.pdf_file_path and self.current_pdf_page < self.pdf_page_count - 1:
            self.load_pdf_page(self.current_pdf_page + 1)
            
    def show_go_to_page_dialog(self):
        """显示页面跳转对话框"""
        if not self.pdf_file_path or self.pdf_page_count <= 1:
            return
            
        page, ok = QInputDialog.getInt(
            self.parent,
            "跳转到页面",
            f"请输入页码 (1-{self.pdf_page_count}):",
            self.current_pdf_page + 1,
            1,
            self.pdf_page_count
        )
        
        if ok:
            self.load_pdf_page(page - 1)  # 转换为从0开始的索引

    def export_to_excel(self, annotations):
        """导出标注列表到Excel文件"""
        if not HAS_EXCEL_SUPPORT:
            QMessageBox.warning(
                self.parent, "功能缺失",
                "缺少 'openpyxl' 库，无法导出Excel。\n请运行: pip install openpyxl"
            )
            return

        if not annotations:
            QMessageBox.information(self.parent, "提示", "标注列表为空，无需导出。")
            return

        default_filename = f"{Path(self.current_file_path).stem}_标注列表.xlsx" if self.current_file_path else "标注列表.xlsx"
        file_path, _ = QFileDialog.getSaveFileName(
            self.parent, "导出为Excel文件", default_filename, "Excel 文件 (*.xlsx)"
        )

        if not file_path:
            return

        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment

            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "标注数据"

            # 设置表头
            headers = ["序号", "类型", "尺寸", "上公差", "下公差", "已审核"]
            ws.append(headers)

            header_font = Font(bold=True)
            for cell in ws[1]:
                cell.font = header_font
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 添加数据
            for ann in annotations:
                row_data = [
                    str(ann.annotation_id),
                    ann.dimension_type,
                    ann.dimension,
                    ann.upper_tolerance,
                    ann.lower_tolerance,
                    "是" if ann.is_audited else "否"
                ]
                ws.append(row_data)

            # 自动调整列宽
            for col_idx, column in enumerate(ws.columns):
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(col_idx + 1)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = max(max_length + 2, len(headers[col_idx]) + 2)
                ws.column_dimensions[column_letter].width = adjusted_width if adjusted_width < 50 else 50

            wb.save(file_path)
            QMessageBox.information(self.parent, "导出成功", f"标注列表已成功导出到:\n{file_path}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(f"成功导出到 {Path(file_path).name}", 5000)

        except Exception as e:
            QMessageBox.critical(self.parent, "导出失败", f"导出到Excel时发生错误:\n{e}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage("导出失败", 3000)

    def export_to_template(self, annotations):
        """将标注列表导出到Excel模板中"""
        try:
            import xlwings as xw
            HAS_XLWINGS_SUPPORT = True
        except ImportError:
            HAS_XLWINGS_SUPPORT = False
            if not HAS_EXCEL_SUPPORT:
                QMessageBox.warning(
                    self.parent, "功能缺失",
                    "缺少Excel支持库。\n请运行: pip install xlwings 或 pip install openpyxl"
                )
                return

        if not annotations:
            QMessageBox.information(self.parent, "提示", "标注列表为空，无需导出。")
            return

        # 默认使用根目录下的muban.xlsx
        template_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "muban.xlsx")
        if not os.path.exists(template_path):
            # 如果默认模板不存在，则提示选择
            template_path, _ = QFileDialog.getOpenFileName(
                self.parent, "选择Excel模板文件", "", "Excel 文件 (*.xlsx)"
            )
            if not template_path:
                return

        # 选择保存位置
        default_filename = f"{Path(self.current_file_path).stem}_检验报告.xlsx" if self.current_file_path else "检验报告.xlsx"
        save_path, _ = QFileDialog.getSaveFileName(
            self.parent, "保存Excel文件", default_filename, "Excel 文件 (*.xlsx)"
        )
        if not save_path:
            return

        try:
            # 获取要插入的标注数据
            annotations_data = []
            for ann in annotations:
                row_data = [
                    str(ann.annotation_id),
                    ann.dimension_type,
                    ann.dimension,
                    ann.upper_tolerance,
                    ann.lower_tolerance
                ]
                annotations_data.append(row_data)

            # 排序标注（按ID排序）
            annotations_data.sort(key=lambda x: int(x[0]) if x[0].isdigit() else float('inf'))

            # 确定插入行的范围
            start_row = 14  # 从第14行开始
            insert_count = len(annotations_data)  # 需要插入的行数

            if HAS_XLWINGS_SUPPORT:
                # 使用xlwings插入行
                try:
                    import shutil
                    shutil.copy2(template_path, save_path)

                    app = xw.App(visible=False)
                    wb = app.books.open(save_path)
                    ws = wb.sheets[0]

                    # 插入行
                    ws.range(f"A{start_row}:P{start_row+insert_count-1}").insert('down')

                    # 填充数据
                    for i, row_data in enumerate(annotations_data):
                        row_idx = start_row + i
                        for j, value in enumerate(row_data):
                            col_letter = chr(65 + j)  # A=65, B=66, ...
                            ws.range(f"{col_letter}{row_idx}").value = value

                        # 计算并填充O列和P列
                        dimension = row_data[2] if len(row_data) > 2 else ""
                        upper_tol = row_data[3] if len(row_data) > 3 else ""
                        lower_tol = row_data[4] if len(row_data) > 4 else ""

                        # O列=C+D（尺寸+上公差）
                        try:
                            if dimension and upper_tol:
                                dim_value = float(dimension)
                                tol_value = float(upper_tol.replace('+', '')) if upper_tol.startswith('+') else float(upper_tol)
                                result_value = dim_value + tol_value
                                ws.range(f"O{row_idx}").value = result_value
                        except ValueError:
                            ws.range(f"O{row_idx}").value = ""

                        # P列=C+E（尺寸+下公差）
                        try:
                            if dimension and lower_tol:
                                dim_value = float(dimension)
                                tol_value = float(lower_tol.replace('+', '')) if lower_tol.startswith('+') else float(lower_tol)
                                result_value = dim_value + tol_value
                                ws.range(f"P{row_idx}").value = result_value
                        except ValueError:
                            ws.range(f"P{row_idx}").value = ""

                    wb.save()
                    wb.close()
                    app.quit()

                    QMessageBox.information(self.parent, "导出成功", f"标注列表已成功导出到:\n{save_path}")
                    if hasattr(self.parent, 'status_bar'):
                        self.parent.status_bar.showMessage(f"成功导出到 {Path(save_path).name}", 5000)
                    return

                except Exception as e:
                    QMessageBox.warning(
                        self.parent, "提示",
                        f"使用xlwings导出失败 ({str(e)})，将尝试使用openpyxl。"
                    )

            # 使用openpyxl作为备选方案
            if HAS_EXCEL_SUPPORT:
                import openpyxl

                wb = openpyxl.load_workbook(template_path)
                ws = wb.active

                # 插入行
                ws.insert_rows(start_row, insert_count)

                # 填充数据
                for i, row_data in enumerate(annotations_data):
                    row_idx = start_row + i
                    for j, value in enumerate(row_data):
                        if j < len(row_data):
                            ws.cell(row=row_idx, column=j+1).value = value

                wb.save(save_path)
                QMessageBox.information(self.parent, "导出成功", f"标注列表已成功导出到:\n{save_path}")
                if hasattr(self.parent, 'status_bar'):
                    self.parent.status_bar.showMessage(f"成功导出到 {Path(save_path).name}", 5000)
            else:
                QMessageBox.critical(self.parent, "导出失败", "未找到可用的Excel处理库")

        except Exception as e:
            QMessageBox.critical(self.parent, "导出失败", f"导出到Excel模板时发生错误:\n{e}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage("导出失败", 3000)

    def export_annotated_image(self, current_pixmap, graphics_scene):
        """将当前视图（包含背景和所有标注）导出为图片"""
        if not current_pixmap:
            QMessageBox.warning(self.parent, "无法导出", "当前没有加载任何图片。")
            return

        # 选择保存位置
        default_filename = f"{Path(self.current_file_path).stem}_标注图片.png" if self.current_file_path else "标注图片.png"
        file_path, _ = QFileDialog.getSaveFileName(
            self.parent, "导出标注图片", default_filename, "PNG 图片 (*.png);;JPG 图片 (*.jpg)"
        )

        if not file_path:
            return

        try:
            from PySide6.QtGui import QPainter

            # 创建与原图相同尺寸的图像
            result_image = QPixmap(current_pixmap.size())
            result_image.fill(Qt.white)  # 白色背景

            # 在结果图像上绘制
            painter = QPainter(result_image)
            painter.setRenderHint(QPainter.Antialiasing)

            # 绘制原始图像
            painter.drawPixmap(0, 0, current_pixmap)

            # 绘制场景中的所有标注项
            graphics_scene.render(painter)

            painter.end()

            # 保存图像
            if result_image.save(file_path):
                QMessageBox.information(self.parent, "导出成功", f"标注图片已成功导出到:\n{file_path}")
                if hasattr(self.parent, 'status_bar'):
                    self.parent.status_bar.showMessage(f"成功导出到 {Path(file_path).name}", 5000)
            else:
                QMessageBox.critical(self.parent, "导出失败", "无法保存图片文件。")

        except Exception as e:
            QMessageBox.critical(self.parent, "导出失败", f"导出图片时发生错误:\n{e}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage("导出失败", 3000)

    def convert_pdf_to_images(self):
        """将PDF文件批量转换为PNG图片"""
        if not HAS_OCR_SUPPORT:
            QMessageBox.warning(
                self.parent, "功能缺失",
                "PDF转换功能需要PyMuPDF支持。请安装所需依赖。"
            )
            return

        # 选择PDF文件
        pdf_path, _ = QFileDialog.getOpenFileName(
            self.parent, "选择PDF文件", "", "PDF 文件 (*.pdf)"
        )
        if not pdf_path:
            return

        # 选择输出目录
        output_dir = QFileDialog.getExistingDirectory(
            self.parent, "选择输出目录"
        )
        if not output_dir:
            return

        try:
            # 获取PDF页数
            page_count = FileLoader.get_pdf_page_count(pdf_path)
            if page_count == 0:
                QMessageBox.warning(self.parent, "错误", "无法读取PDF文件或PDF文件不包含任何页面")
                return

            # 显示进度条
            if hasattr(self.parent, 'progress_bar'):
                self.parent.progress_bar.setVisible(True)
                self.parent.progress_bar.setMaximum(page_count)
                self.parent.progress_bar.setValue(0)

            # 获取质量设置
            quality = 4.0
            if hasattr(self.parent, 'pdf_quality_combo'):
                quality_text = self.parent.pdf_quality_combo.currentText()
                quality = PDF_QUALITY_OPTIONS.get(quality_text, 4.0)

            # 转换每一页
            pdf_name = Path(pdf_path).stem
            for page_index in range(page_count):
                if hasattr(self.parent, 'status_bar'):
                    self.parent.status_bar.showMessage(f"正在转换第 {page_index+1}/{page_count} 页...")

                # 创建临时场景
                temp_scene = graphics_scene if hasattr(self.parent, 'graphics_scene') else None
                if not temp_scene:
                    from PySide6.QtWidgets import QGraphicsScene
                    temp_scene = QGraphicsScene()

                # 加载PDF页面
                pixmap, temp_path = FileLoader.load_pdf(
                    pdf_path, temp_scene, page_index, quality=quality
                )

                if pixmap and not pixmap.isNull():
                    # 保存为PNG
                    output_filename = f"{pdf_name}_page_{page_index+1:03d}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    pixmap.save(output_path, "PNG")

                    # 清理临时文件
                    if temp_path and os.path.exists(temp_path):
                        try:
                            os.remove(temp_path)
                        except:
                            pass

                # 更新进度
                if hasattr(self.parent, 'progress_bar'):
                    self.parent.progress_bar.setValue(page_index + 1)
                QApplication.processEvents()

            # 隐藏进度条
            if hasattr(self.parent, 'progress_bar'):
                self.parent.progress_bar.setVisible(False)

            QMessageBox.information(
                self.parent, "转换完成",
                f"PDF文件已成功转换为 {page_count} 张PNG图片\n保存位置: {output_dir}"
            )
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(f"PDF转换完成，共 {page_count} 张图片", 5000)

        except Exception as e:
            if hasattr(self.parent, 'progress_bar'):
                self.parent.progress_bar.setVisible(False)
            QMessageBox.critical(self.parent, "错误", f"转换过程中发生错误: {str(e)}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(f"❌ PDF转换错误: {str(e)}", 5000)
