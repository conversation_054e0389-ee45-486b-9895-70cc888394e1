#!/usr/bin/env python3
"""
工作线程模块 - 处理后台任务
"""

import time
import logging
from pathlib import Path
from typing import Optional

from PySide6.QtCore import QObject, QRunnable, Signal, QEvent
from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import QGraphicsScene

from core.file_loader import FileLoader
from utils.constants import SUPPORTED_IMAGE_FORMATS, SUPPORTED_PDF_FORMATS, SUPPORTED_DXF_FORMATS

# 获取已配置的logger
logger = logging.getLogger('PyQtBubble.Workers')

class LoadPDFEvent(QEvent):
    """自定义事件用于将PDF加载结果从线程传递到主线程"""
    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())
    
    def __init__(self, pixmap, temp_path):
        super().__init__(self.EVENT_TYPE)
        self.pixmap = pixmap
        self.temp_path = temp_path

class PDFLoaderSignals(QObject):
    """PDF加载工作线程的信号类"""
    finished = Signal(QPixmap, str)  # 成功加载后发出信号：pixmap, 临时文件路径
    error = Signal(str)  # 加载出错时发出信号
    progress = Signal(int)  # 加载进度信号

class PDFLoaderWorker(QRunnable):
    """PDF加载工作线程"""
    def __init__(self, pdf_path, page_index, quality=4.0, force_resolution=False):
        super().__init__()
        self.pdf_path = pdf_path
        self.page_index = page_index
        self.quality = quality
        self.force_resolution = force_resolution
        self.signals = PDFLoaderSignals()
        
    def run(self):
        """执行PDF加载"""
        try:
            start_time = time.time()
            logger.debug(f"开始加载PDF页面: {self.page_index+1}, 质量: {self.quality}, 强制分辨率: {self.force_resolution}")
            
            self.signals.progress.emit(10)
            # 创建一个临时场景，用于在线程中处理
            temp_scene = QGraphicsScene()
            logger.debug(f"创建临时场景耗时: {time.time() - start_time:.2f}秒")
            
            self.signals.progress.emit(30)
            
            # 调用FileLoader加载PDF
            logger.debug(f"开始调用FileLoader.load_pdf...")
            load_start = time.time()
            pixmap, temp_path = FileLoader.load_pdf(
                self.pdf_path, temp_scene, self.page_index, quality=self.quality,
                force_resolution=self.force_resolution
            )
            logger.debug(f"FileLoader.load_pdf完成，耗时: {time.time() - load_start:.2f}秒")
            
            self.signals.progress.emit(90)
            
            if pixmap and not pixmap.isNull() and temp_path:
                logger.debug(f"PDF页面加载成功: {self.page_index+1}, 尺寸: {pixmap.width()}x{pixmap.height()}")
                # 成功加载，发送信号
                self.signals.finished.emit(pixmap, temp_path)
            else:
                logger.error(f"PDF页面加载失败: pixmap为空或temp_path为空")
                # 加载失败
                self.signals.error.emit("无法加载PDF页面")
                
            logger.debug(f"PDF加载线程总耗时: {time.time() - start_time:.2f}秒")
                
        except Exception as e:
            logger.exception(f"PDF加载过程中发生异常: {str(e)}")
            # 处理异常
            self.signals.error.emit(str(e))

class FileLoaderSignals(QObject):
    """文件加载工作线程的信号类"""
    finished = Signal(str, QPixmap)  # 成功加载后发出信号：文件路径, 图像数据
    pdf_loaded = Signal(str, int)  # PDF加载成功的信号：文件路径, 页数
    error = Signal(str)  # 加载出错时发出信号
    progress = Signal(int, str)  # 加载进度信号：进度值, 描述

class FileLoaderWorker(QRunnable):
    """文件加载工作线程"""
    def __init__(self, file_path, pdf_quality="高清 (4x)"):
        super().__init__()
        self.file_path = file_path
        self.pdf_quality = pdf_quality
        self.signals = FileLoaderSignals()
        
    def run(self):
        """执行文件加载"""
        try:
            file_path = Path(self.file_path)
            extension = file_path.suffix.lower()
            
            self.signals.progress.emit(10, f"正在加载文件 {file_path.name}...")
            
            # 处理图像文件
            if extension in SUPPORTED_IMAGE_FORMATS:
                self.signals.progress.emit(30, f"正在加载图像 {file_path.name}...")
                pixmap = FileLoader.load_image(str(file_path))
                if pixmap:
                    self.signals.progress.emit(90, "图像加载成功")
                    self.signals.finished.emit(str(file_path), pixmap)
                else:
                    self.signals.error.emit(f"无法加载图像文件: {file_path.name}")
                    
            # 处理PDF文件
            elif extension in SUPPORTED_PDF_FORMATS:
                self.signals.progress.emit(20, f"正在分析PDF文件 {file_path.name}...")
                
                # 获取PDF页数
                page_count = FileLoader.get_pdf_page_count(str(file_path))
                if page_count == 0:
                    self.signals.error.emit("无法读取PDF文件或PDF文件不包含任何页面")
                    return
                
                # 通知PDF加载成功
                self.signals.progress.emit(80, f"PDF文件加载成功，共 {page_count} 页")
                self.signals.pdf_loaded.emit(str(file_path), page_count)
                    
            # 处理DXF文件
            elif extension in SUPPORTED_DXF_FORMATS:
                self.signals.error.emit("DXF文件加载尚未实现多线程支持")
                
            else:
                self.signals.error.emit(f"不支持的文件格式: {extension}")
                
        except Exception as e:
            self.signals.error.emit(f"加载文件时发生错误: {str(e)}")
