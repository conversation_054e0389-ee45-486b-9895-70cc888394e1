#!/usr/bin/env python3
"""
OCR管理模块 - 处理OCR识别相关功能
"""

import os
import tempfile
import time
import logging
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QSlider, 
    QCheckBox, QPushButton, QProgressBar, QSpinBox, QMessageBox
)
from PySide6.QtCore import Qt, QRectF, QPointF, Signal, QObject
from PySide6.QtGui import QPixmap, QColor, QPainterPath, QPen, QBrush

from utils.constants import (
    DEFAULT_OCR_LANGUAGES, OCR_FILTER_OPTIONS, OCR_FILTER_TYPE_MAP,
    OCR_TEXT_TYPE_COLORS, OCR_TYPE_TO_STYLE
)
from utils.dependencies import HAS_PADDLE_OCR, HAS_OCR_SUPPORT

if HAS_PADDLE_OCR:
    from core.paddle_ocr_worker import PaddleOCRWorker

# 获取已配置的logger
logger = logging.getLogger('PyQtBubble.OCRManager')

class OCRManager(QObject):
    """OCR管理器 - 处理所有OCR相关功能"""
    
    # 信号定义
    ocr_finished = Signal(list, list)  # OCR完成信号：results, existing_results
    ocr_progress = Signal(int)  # OCR进度信号
    ocr_error = Signal(str)  # OCR错误信号
    area_ocr_finished = Signal(list, QRectF, str, int, int, bool)  # 区域OCR完成信号
    area_ocr_error = Signal(str)  # 区域OCR错误信号
    stats_updated = Signal(str)  # 统计信息更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        
        # OCR相关属性
        self.ocr_worker = None
        self.area_ocr_worker = None
        self.ocr_results = []
        self.ocr_box_color = QColor(91, 192, 235, 120)  # 默认OCR框颜色
        
        # UI组件（将在setup_ui中创建）
        self.language_combo = None
        self.confidence_slider = None
        self.confidence_label = None
        self.enhance_contrast_cb = None
        self.denoise_cb = None
        self.binarize_cb = None
        self.gpu_checkbox = None
        self.cpu_checkbox = None
        self.threads_spinbox = None
        self.ocr_button = None
        self.create_all_btn = None
        self.clear_ocr_btn = None
        self.progress_bar = None
        self.ocr_stats_label = None
        self.filter_combo = None
        
    def setup_compact_ocr_panel(self, parent_layout):
        """设置紧凑的OCR控制面板"""
        ocr_widget = QWidget()
        ocr_widget.setMaximumHeight(200)
        ocr_layout = QVBoxLayout(ocr_widget)
        ocr_layout.setContentsMargins(5, 5, 5, 5)
        ocr_layout.setSpacing(3)
        
        # 第一行：语言和置信度
        row1_layout = QHBoxLayout()
        row1_layout.addWidget(QLabel("语言:"))
        self.language_combo = QComboBox()
        self.language_combo.addItems(list(DEFAULT_OCR_LANGUAGES.keys()))
        self.language_combo.setCurrentText("中文+英文")
        row1_layout.addWidget(self.language_combo)
        
        row1_layout.addWidget(QLabel("置信度:"))
        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setRange(10, 90)
        self.confidence_slider.setValue(30)
        self.confidence_slider.setMaximumWidth(80)
        self.confidence_label = QLabel("0.30")
        self.confidence_label.setMinimumWidth(40)
        row1_layout.addWidget(self.confidence_slider)
        row1_layout.addWidget(self.confidence_label)
        ocr_layout.addLayout(row1_layout)
        
        # 第二行：预处理选项
        row2_layout = QHBoxLayout()
        self.enhance_contrast_cb = QCheckBox("增强对比度")
        self.enhance_contrast_cb.setChecked(True)
        row2_layout.addWidget(self.enhance_contrast_cb)
        
        self.denoise_cb = QCheckBox("降噪处理")
        self.denoise_cb.setChecked(True)
        row2_layout.addWidget(self.denoise_cb)
        
        self.binarize_cb = QCheckBox("二值化")
        self.binarize_cb.setChecked(False)
        row2_layout.addWidget(self.binarize_cb)
        ocr_layout.addLayout(row2_layout)
        
        # 第三行：GPU/CPU选项和线程数
        row2_5_layout = QHBoxLayout()
        self.gpu_checkbox = QCheckBox("GPU加速")
        self.gpu_checkbox.setChecked(True)
        row2_5_layout.addWidget(self.gpu_checkbox)
        
        self.cpu_checkbox = QCheckBox("强制CPU")
        self.cpu_checkbox.setChecked(False)
        row2_5_layout.addWidget(self.cpu_checkbox)
        
        row2_5_layout.addWidget(QLabel("线程数:"))
        self.threads_spinbox = QSpinBox()
        self.threads_spinbox.setRange(1, 16)
        self.threads_spinbox.setValue(4)
        self.threads_spinbox.setMaximumWidth(60)
        self.threads_spinbox.setEnabled(False)  # 默认禁用，因为GPU模式不需要
        row2_5_layout.addWidget(self.threads_spinbox)
        row2_5_layout.addStretch()
        ocr_layout.addLayout(row2_5_layout)
        
        # 第四行：操作按钮
        row3_layout = QHBoxLayout()
        self.ocr_button = QPushButton("🔍 开始OCR识别")
        self.ocr_button.setMaximumWidth(120)
        row3_layout.addWidget(self.ocr_button)
        
        self.create_all_btn = QPushButton("全部标注")
        self.create_all_btn.setMaximumWidth(80)
        row3_layout.addWidget(self.create_all_btn)
        
        self.clear_ocr_btn = QPushButton("清除OCR")
        self.clear_ocr_btn.setMaximumWidth(80)
        row3_layout.addWidget(self.clear_ocr_btn)
        ocr_layout.addLayout(row3_layout)
        
        # 进度条和统计信息
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(15)
        ocr_layout.addWidget(self.progress_bar)
        
        self.ocr_stats_label = QLabel("识别结果: 0个文本")
        self.ocr_stats_label.setStyleSheet(
            "QLabel { background-color: transparent; border: none; padding: 4px; "
            "color: #6c757d; font-size: 11px; }"
        )
        ocr_layout.addWidget(self.ocr_stats_label)
        
        # 筛选控件
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("筛选:"))
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(OCR_FILTER_OPTIONS)
        filter_layout.addWidget(self.filter_combo)
        filter_layout.addStretch()
        ocr_layout.addLayout(filter_layout)
        
        parent_layout.addWidget(ocr_widget)
        
    def setup_connections(self):
        """设置信号槽连接"""
        if self.confidence_slider:
            self.confidence_slider.valueChanged.connect(
                lambda v: self.confidence_label.setText(f"{v/100:.2f}")
            )
        if self.ocr_button:
            self.ocr_button.clicked.connect(self.start_ocr_recognition)
        if self.create_all_btn:
            self.create_all_btn.clicked.connect(self.create_annotations_from_ocr)
        if self.clear_ocr_btn:
            self.clear_ocr_btn.clicked.connect(self.clear_ocr_results)
        if self.filter_combo:
            self.filter_combo.currentTextChanged.connect(self.filter_ocr_results)
        if self.gpu_checkbox:
            self.gpu_checkbox.toggled.connect(self.on_gpu_checkbox_toggled)
        if self.cpu_checkbox:
            self.cpu_checkbox.toggled.connect(self.on_cpu_checkbox_toggled)
            
    def start_ocr_recognition(self):
        """启动OCR识别过程"""
        if not HAS_OCR_SUPPORT:
            QMessageBox.warning(
                self.parent, "功能缺失", 
                "OCR功能需要PaddleOCR和依赖包。请安装所需依赖。"
            )
            return
        
        if not hasattr(self.parent, 'current_pixmap') or not self.parent.current_pixmap:
            QMessageBox.information(self.parent, "提示", "请先打开图片文件。")
            return
            
        # 保存已有的OCR结果，而不是清除
        existing_results = self.ocr_results.copy()
        # 只清除显示，不清除结果数据
        self.clear_ocr_display()
        
        # 获取语言配置
        lang_text = self.language_combo.currentText()
        lang_code = DEFAULT_OCR_LANGUAGES.get(lang_text, ["ch_sim"])
        
        # 获取环境配置
        force_cpu = self.cpu_checkbox.isChecked()
        use_gpu = self.gpu_checkbox.isChecked() and not force_cpu
        
        # 获取CPU线程数
        cpu_threads = self.threads_spinbox.value()
        
        # 显示设备模式
        device_mode = "CPU" if force_cpu else ("GPU" if use_gpu else "自动")
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(f"正在进行OCR文本识别... (使用{device_mode}模式)")
        
        # 获取屏蔽区域数据
        masked_regions_data = []
        if hasattr(self.parent, 'masked_regions'):
            masked_regions_data = [
                {'x': r.x(), 'y': r.y(), 'width': r.width(), 'height': r.height()} 
                for r in self.parent.masked_regions
            ]
        
        # 创建OCR工作器
        self.ocr_worker = PaddleOCRWorker(
            self.parent.current_file_path, 
            lang_code, 
            masked_regions_data,
            force_cpu=force_cpu,
            cpu_threads=cpu_threads,
            direct_recognition=False  # 全图OCR不使用直接识别模式
        )
        
        # 连接信号
        self.ocr_worker.signals.progress.connect(self.on_ocr_progress)
        self.ocr_worker.signals.error.connect(self.on_ocr_error)
        
        # 修改on_ocr_finished连接，合并现有结果
        self.ocr_worker.signals.finished.connect(
            lambda results: self.on_ocr_finished(results, existing_results)
        )
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 启动线程
        if hasattr(self.parent, 'thread_pool'):
            self.parent.thread_pool.start(self.ocr_worker)
            
    def on_ocr_progress(self, progress: int):
        """OCR进度更新"""
        self.progress_bar.setValue(progress)
        self.ocr_progress.emit(progress)

    def on_ocr_error(self, error_msg: str):
        """OCR错误处理"""
        self.ocr_button.setEnabled(True)
        self.ocr_button.setText("🔍 开始OCR识别")
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self.parent, "OCR识别错误", error_msg)
        self.ocr_error.emit(error_msg)
        
    def on_ocr_finished(self, results: List[dict], existing_results: List[dict] = None):
        """OCR识别完成处理"""
        self.ocr_button.setEnabled(True)
        self.ocr_button.setText("🔍 开始OCR识别")
        self.progress_bar.setVisible(False)
        
        # 合并相邻的OCR结果
        if results:
            results = self.merge_adjacent_ocr_results(results)
        
        # 合并现有结果和新结果
        if existing_results:
            self.ocr_results = existing_results + results
        else:
            self.ocr_results = results
            
        # 显示结果
        self.display_ocr_results()
        self.update_ocr_stats()
        
        # 发出完成信号
        self.ocr_finished.emit(results, existing_results or [])
        
        # 显示完成消息
        total_count = len(self.ocr_results)
        new_count = len(results)
        message = f"OCR识别完成！\n本次识别: {new_count}个文本\n总计: {total_count}个文本"
        
        now = datetime.now()
        message += f"\n完成时间: {now.strftime('%Y-%m-%d %H:%M:%S')}"
        
        QMessageBox.information(self.parent, "OCR识别完成", message)
        
    def merge_adjacent_ocr_results(self, results: List[dict]) -> List[dict]:
        """合并可能属于同一尺寸标注的相邻OCR结果"""
        if not results or len(results) <= 1:
            return results
            
        # 按照y坐标排序结果，对于相同y坐标的，按照x坐标排序
        sorted_results = sorted(results, key=lambda r: (r.get('center_y', 0), r.get('center_x', 0)))
        
        # 这里可以添加更复杂的合并逻辑
        # 目前先返回排序后的结果
        return sorted_results
        
    def clear_ocr_display(self):
        """清除OCR结果的可视化显示"""
        try:
            if hasattr(self.parent, 'graphics_scene'):
                # 找出所有OCR边界框项目并移除
                bbox_items = []
                for item in self.parent.graphics_scene.items():
                    if hasattr(item, 'is_ocr_bbox') and item.is_ocr_bbox:
                        bbox_items.append(item)
                
                for item in bbox_items:
                    try:
                        self.parent.graphics_scene.removeItem(item)
                    except Exception as e:
                        print(f"移除OCR边界框时出错: {e}")
        except Exception as e:
            print(f"清除OCR显示时出错: {e}")
            
    def clear_ocr_results(self):
        """清除OCR结果"""
        try:
            self.clear_ocr_display()
            self.ocr_results = []
            
            # 更新统计信息
            self.update_ocr_stats()
            
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage("已清除所有OCR结果", 2000)
        except Exception as e:
            print(f"清除OCR结果时出错: {e}")
            # 确保OCR结果被清空
            self.ocr_results = []
            
    def update_ocr_stats(self):
        """更新OCR统计信息"""
        total_count = len(self.ocr_results)
        type_counts = {}
        for result in self.ocr_results: 
            result_type = result.get('type', 'unknown')
            type_counts[result_type] = type_counts.get(result_type, 0) + 1
        
        stats_text = f"识别结果: {total_count}个文本"
        if type_counts: 
            stats_text += f" ({', '.join([f'{k}({v})' for k, v in type_counts.items()])})"
        
        self.ocr_stats_label.setText(stats_text)
        self.stats_updated.emit(stats_text)
        
    def display_ocr_results(self):
        """显示OCR结果"""
        self.clear_ocr_display()
        for i, result in enumerate(self.ocr_results): 
            self.create_ocr_bbox_item(result, i)
            
    def create_ocr_bbox_item(self, ocr_result, index):
        """创建OCR边界框显示项"""
        if not HAS_OCR_SUPPORT or not hasattr(self.parent, 'graphics_scene'):
            return
            
        bbox = ocr_result['bbox']
        bbox_array = np.array(bbox)
        path = QPainterPath()
        
        # 创建路径
        if len(bbox_array) >= 4:
            path.moveTo(bbox_array[0][0], bbox_array[0][1])
            for point in bbox_array[1:]:
                path.lineTo(point[0], point[1])
            path.closeSubpath()
        
        # 创建图形项
        from ui.graphics_view import ResizableGraphicsPathItem
        bbox_item = ResizableGraphicsPathItem(path)
        bbox_item.is_ocr_bbox = True
        bbox_item.ocr_result_index = index
        
        # 设置样式
        text_type = ocr_result.get('type', 'annotation')
        color = OCR_TEXT_TYPE_COLORS.get(text_type, (91, 192, 235, 120))
        bbox_item.setPen(QPen(QColor(*color[:3]), 2))
        bbox_item.setBrush(QBrush(QColor(*color)))
        
        # 连接信号
        if hasattr(bbox_item, 'bbox_updated'):
            bbox_item.bbox_updated.connect(self.on_bbox_updated)
        
        self.parent.graphics_scene.addItem(bbox_item)
        return bbox_item
        
    def filter_ocr_results(self):
        """筛选OCR结果，仅显示符合条件的结果"""
        try:
            # 获取筛选条件
            filter_type = self.filter_combo.currentText()
            
            if filter_type == "全部":
                # 显示所有结果
                self.display_ocr_results()
            else:
                # 筛选特定类型
                target_type = OCR_FILTER_TYPE_MAP.get(filter_type)
                if target_type:
                    self.clear_ocr_display()
                    for i, result in enumerate(self.ocr_results):
                        if result.get('type') == target_type:
                            self.create_ocr_bbox_item(result, i)
        except Exception as e:
            print(f"筛选OCR结果时出错: {e}")
            # 出错时显示全部结果
            self.display_ocr_results()
            
    def create_annotations_from_ocr(self):
        """从OCR结果创建标注"""
        if not self.ocr_results:
            QMessageBox.information(self.parent, "提示", "请先进行OCR识别")
            return
            
        # 发出信号，让主窗口处理标注创建
        # 这里需要主窗口实现相应的处理方法
        pass
        
    def on_gpu_checkbox_toggled(self, checked):
        """当GPU复选框状态变化时，更新CPU复选框状态"""
        if checked and self.cpu_checkbox.isChecked():
            self.cpu_checkbox.blockSignals(True)
            self.cpu_checkbox.setChecked(False)
            self.cpu_checkbox.blockSignals(False)
            # 禁用线程数输入框
            self.threads_spinbox.setEnabled(False)

    def on_cpu_checkbox_toggled(self, checked):
        """当CPU复选框状态变化时，更新GPU复选框状态"""
        if checked and self.gpu_checkbox.isChecked():
            self.gpu_checkbox.blockSignals(True)
            self.gpu_checkbox.setChecked(False)
            self.gpu_checkbox.blockSignals(False)
        # 根据CPU选择状态启用/禁用线程数输入框    
        self.threads_spinbox.setEnabled(checked)
        
    def on_bbox_updated(self, bbox_item):
        """当OCR边界框被用户手动调整后调用"""
        # 这里可以添加边界框更新的处理逻辑
        pass
