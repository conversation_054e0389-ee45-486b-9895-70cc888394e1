# core/annotation_item.py

import math
from typing import Optional
import time # Added for debouncing

from PySide6.QtWidgets import QGraphicsObject, QMenu, QColorDialog
from PySide6.QtCore import Qt, Signal, QRectF, QPointF
from PySide6.QtGui import (
    QPainter, QPen, QBrush, QColor, QPainterPath, QFont, QPolygonF, QPainterPathStroker
)

class BubbleAnnotationItem(QGraphicsObject):
    """
    【功能增强版】气泡标注图形项
    - 新增审核状态属性
    - 新增自动适应识别框尺寸功能
    """
    # --- 已有信号 ---
    size_change_requested = Signal(object)
    shape_change_requested = Signal(object)
    style_change_requested = Signal(object)
    color_change_requested = Signal(object)
    selected = Signal(object)
    moved = Signal(object, QPointF)
    delete_requested = Signal(object)
    data_updated = Signal(object)

    def __init__(self, annotation_id: int, anchor_point: QPointF, text: str = "", style: str = "default", 
                 shape: str = "circle", color: Optional[QColor] = None, size: int = 15,
                 dimension: str = "", dimension_type: str = "", 
                 upper_tolerance: str = "", lower_tolerance: str = "",
                 # --- 新增属性 ---
                 is_audited: bool = False):
        super().__init__()
        self.annotation_id = annotation_id
        self.text = text or f"标注 {annotation_id}"
        self.style = style
        self.arrow_head_size = 10
        self.shape_type = shape
        self.custom_color = color
        self.radius = size
        self.base_radius = size  # 基准半径
        self.scale_factor = 1.0  # 大小比例因子
        
        self.dimension = dimension
        self.dimension_type = dimension_type
        self.upper_tolerance = upper_tolerance
        self.lower_tolerance = lower_tolerance
        
        # --- 初始化新增属性 ---
        self.is_audited = is_audited
        self.bbox_points = []  # 存储边界框各点坐标
        self.auto_radius = False  # 自动计算半径标志
        self.edge_attachment_ratio = 0.5  # 箭头在边框上的相对位置比例，默认在中点
        
        self.anchor_point = anchor_point
        self._is_highlighted = False
        self._cached_shape_path = QPainterPath()

        self.setFlags(
            QGraphicsObject.ItemIsSelectable | QGraphicsObject.ItemIsMovable | QGraphicsObject.ItemSendsGeometryChanges
        )
        self.setCacheMode(QGraphicsObject.DeviceCoordinateCache)
        self.setAcceptedMouseButtons(Qt.LeftButton | Qt.RightButton)

        initial_bubble_position = anchor_point + QPointF(50, 0)
        self.setPos(initial_bubble_position)
        
        self._update_geometry()
        
    def update_annotation_id_display(self):
        """更新气泡标注ID显示
        
        用于重新排序后强制刷新气泡显示的方法
        """
        self.prepareGeometryChange()
        self.update()  # 触发重绘

    # --- Setter 方法 ---
    def set_dimension(self, value: str):
        if self.dimension != value:
            self.dimension = value
            self.data_updated.emit(self)

    def set_dimension_type(self, value: str):
        if self.dimension_type != value:
            self.dimension_type = value
            self.data_updated.emit(self)

    def set_upper_tolerance(self, value: str):
        if self.upper_tolerance != value:
            self.upper_tolerance = value
            self.data_updated.emit(self)

    def set_lower_tolerance(self, value: str):
        if self.lower_tolerance != value:
            self.lower_tolerance = value
            self.data_updated.emit(self)

    def set_text(self, text: str):
        if self.text != text:
            self.text = text
            self.data_updated.emit(self)
            
    def set_audited(self, audited: bool):
        """【新增】设置审核状态的方法"""
        if self.is_audited != audited:
            self.is_audited = audited
            self.data_updated.emit(self) # 发射信号，通知UI更新
            
    def get_style_colors(self):
        if self.custom_color and self.custom_color.isValid():
            return {
                "normal_pen": self.custom_color, "normal_brush": QColor(self.custom_color.red(), self.custom_color.green(), self.custom_color.blue(), 100),
                "selected_pen": self.custom_color.darker(150), "selected_brush": QColor(self.custom_color.red(), self.custom_color.green(), self.custom_color.blue(), 150)
            }
        styles = {
            "default": {"normal_pen": QColor(0, 0, 255), "normal_brush": QColor(255, 255, 255, 200), "selected_pen": QColor(255, 0, 0), "selected_brush": QColor(255, 255, 0, 100)},
            "warning": {"normal_pen": QColor(255, 165, 0), "normal_brush": QColor(255, 248, 220, 200), "selected_pen": QColor(255, 69, 0), "selected_brush": QColor(255, 218, 185, 150)},
            "error": {"normal_pen": QColor(220, 20, 60), "normal_brush": QColor(255, 192, 203, 200), "selected_pen": QColor(178, 34, 34), "selected_brush": QColor(255, 160, 122, 150)},
            "success": {"normal_pen": QColor(34, 139, 34), "normal_brush": QColor(240, 255, 240, 200), "selected_pen": QColor(0, 128, 0), "selected_brush": QColor(144, 238, 144, 150)}
        }
        return styles.get(self.style, styles["default"])

    def _update_geometry(self):
        # 防止在_is_updating_size状态下重新计算半径
        is_being_updated = getattr(self, '_is_updating_size', False)
        
        # 只有当不在更新状态且启用了自动半径时，才重新计算半径
        # 这可以防止递归调用
        if not is_being_updated and self.auto_radius:
            # 直接计算半径，而不是调用_calculate_radius_from_bbox方法
            if not hasattr(self, 'base_radius'):
                self.base_radius = 20  # 默认基础半径
            if not hasattr(self, 'scale_factor'):
                self.scale_factor = 1.0
            self.radius = max(int(self.base_radius * self.scale_factor), 10)
        
        # 确保考虑文本大小的影响，尤其是当文本可能比气泡大时
        # 文本大小是气泡半径的1.2倍，确保气泡的绘制半径足够大
        actual_drawing_radius = self.radius
            
        path = QPainterPath()
        if self.shape_type in ["circle", "solid_circle"]:
            path.addEllipse(QPointF(0, 0), actual_drawing_radius, actual_drawing_radius)
        elif self.shape_type == "pentagram":
            path.addPolygon(self._create_star_polygon())
        elif self.shape_type == "triangle":
            path.addPolygon(self._create_triangle_polygon())
        else: # 默认圆形
            path.addEllipse(QPointF(0, 0), actual_drawing_radius, actual_drawing_radius)

        bubble_center_local = QPointF(0, 0)
        anchor_local = self.mapFromScene(self.anchor_point)
        target_point = self._get_target_point(anchor_local)
        line_vector = target_point - bubble_center_local
        distance = math.hypot(line_vector.x(), line_vector.y())

        if distance > actual_drawing_radius:
            line_path = QPainterPath()
            start_point = bubble_center_local + line_vector * (actual_drawing_radius / distance)
            line_path.moveTo(start_point)
            line_path.lineTo(target_point)
            
            stroker = QPainterPathStroker()
            stroker.setWidth(self.arrow_head_size)
            path.addPath(stroker.createStroke(line_path))
        
        self._cached_shape_path = path
        
        # 强制更新
        self.prepareGeometryChange()

    def _calculate_radius_from_bbox(self):
        """计算气泡半径，使用统一的基础大小值"""
        # 使用统一的基础半径，不再依赖于各个边界框的尺寸
        # 这将确保所有气泡具有相同的基础大小
        
        # 设置统一的基础半径值
        self.base_radius = 20  # 默认基础半径
        
        # 应用当前的大小比例 (确保使用当前设置的scale_factor)
        if not hasattr(self, 'scale_factor'):
            self.scale_factor = 1.0
            
        # 调试信息
        print(f"设置气泡 {self.annotation_id} 基准半径: {self.base_radius}, 比例因子: {self.scale_factor}")
        
        # 计算实际半径并设置
        self.radius = max(int(self.base_radius * self.scale_factor), 10)
        print(f"  -> 最终半径: {self.radius}, 气泡直径: {self.radius*2}")

    def shape(self) -> QPainterPath:
        return self._cached_shape_path

    def boundingRect(self) -> QRectF:
        # 获取基本图形路径的边界矩形
        base_rect = self._cached_shape_path.controlPointRect()
        
        # 考虑到文本大小可能超出基本图形，扩展边界矩形
        # 文本大小是气泡半径的1.2倍，我们在各个方向多预留20%的空间
        text_margin = self.radius * 0.4  # 提供比实际字体大小更多的边距
        
        # 扩展矩形的各个方向
        expanded_rect = QRectF(
            base_rect.left() - text_margin,
            base_rect.top() - text_margin,
            base_rect.width() + text_margin * 2,
            base_rect.height() + text_margin * 2
        )
        
        return expanded_rect

    def paint(self, painter: QPainter, option, widget=None):
        painter.setRenderHint(QPainter.Antialiasing, True)
        
        colors = self.get_style_colors()
        pen = QPen(colors["selected_pen"], 2) if self.isSelected() or self._is_highlighted else QPen(colors["normal_pen"], 1)
        brush = QBrush(colors["selected_brush"]) if self.isSelected() or self._is_highlighted else QBrush(colors["normal_brush"])
        painter.setPen(pen)
        
        bubble_center_local = QPointF(0, 0)
        anchor_local = self.mapFromScene(self.anchor_point)
        
        # 获取箭头目标点
        target_point = self._get_target_point(anchor_local)
        
        # 计算从标注中心到目标点的向量
        line_vector = target_point - bubble_center_local
        distance = math.hypot(line_vector.x(), line_vector.y())
        
        if distance > self.radius:
            # 计算标注边缘上的出发点
            start_point = bubble_center_local + line_vector * (self.radius / distance)
            
            # 绘制从标注边缘到目标点的线段
            painter.drawLine(start_point, target_point)
            
            # 绘制箭头头部
            self._draw_arrowhead(painter, start_point, target_point)

        if self.shape_type == "solid_circle":
            painter.setBrush(pen.color())
        else:
            painter.setBrush(brush)
            
        if self.shape_type in ["circle", "solid_circle"]:
            painter.drawEllipse(bubble_center_local, self.radius, self.radius)
        elif self.shape_type == "pentagram":
            painter.drawPolygon(self._create_star_polygon())
        elif self.shape_type == "triangle":
            painter.drawPolygon(self._create_triangle_polygon())
        else:
            painter.drawEllipse(bubble_center_local, self.radius, self.radius)

        # 文本绘制部分
        painter.setPen(QPen(QColor(0, 0, 0)))
        
        # 获取标注ID文本
        id_text = str(self.annotation_id)
        
        # 根据文本长度动态调整字体大小
        # 对于三位数及以上，使用更小的字体
        if len(id_text) >= 3:
            font_size = max(int(self.radius * 0.85), 8)  # 三位数使用更小的字体
        else:
            font_size = max(int(self.radius * 1.0), 10)  # 一两位数使用原来的字体大小
            
        font = QFont("Arial", font_size, QFont.ExtraBold)  # 使用粗体
        painter.setFont(font)
        
        # 创建一个更大的矩形用于文本绘制
        # 对于三位数及以上，使用更大的文本区域
        if len(id_text) >= 3:
            text_rect_size = self.radius * 1.8  # 增加文本区域大小
        else:
            text_rect_size = self.radius * 1.6  # 保持原来的大小
            
        text_rect = QRectF(
            -text_rect_size/2, 
            -text_rect_size/2, 
            text_rect_size, 
            text_rect_size
        )
        
        painter.drawText(text_rect, Qt.AlignCenter, id_text)

    def _get_target_point(self, anchor_point: QPointF) -> QPointF:
        """根据设置获取箭头指向点的位置"""
        try:
            # 如果有具体的边界框信息，使用实际边界框计算指向点位置
            if self.bbox_points and len(self.bbox_points) >= 4:
                try:
                    # 获取气泡在场景中的位置
                    bubble_scene_pos = self.scenePos()
                    
                    # 确保edge_attachment_ratio是有效值（在0-1之间）
                    if not hasattr(self, 'edge_attachment_ratio') or self.edge_attachment_ratio is None:
                        self.edge_attachment_ratio = 0.5
                    else:
                        # 确保值在0-1范围内
                        self.edge_attachment_ratio = max(0.0, min(1.0, self.edge_attachment_ratio))
                    
                    # 计算四条边（处理旋转情况）
                    edges = []
                    for i in range(len(self.bbox_points)):
                        start_point = self.bbox_points[i]
                        end_point = self.bbox_points[(i + 1) % len(self.bbox_points)]
                        edges.append((start_point, end_point))
                    
                    # 找出距离气泡最近的边
                    closest_edge = None
                    min_distance = float('inf')
                    
                    for edge in edges:
                        # 计算气泡到边的距离
                        distance = self._point_to_line_distance(bubble_scene_pos, edge[0], edge[1])
                        if distance < min_distance:
                            min_distance = distance
                            closest_edge = edge
                    
                    if closest_edge:
                        # 在最近的边上找出目标点
                        start, end = closest_edge
                        # 使用edge_attachment_ratio计算沿边的位置
                        target_x = start.x() + (end.x() - start.x()) * self.edge_attachment_ratio
                        target_y = start.y() + (end.y() - start.y()) * self.edge_attachment_ratio
                        
                        # 映射边界框指向点到本地坐标
                        return self.mapFromScene(QPointF(target_x, target_y))
                    
                except Exception as e:
                    print(f"计算目标点时出错: {e}")
                    return anchor_point
            
            # 当没有边界框信息时，直接返回锚点作为目标点
            return anchor_point
        except Exception as e:
            print(f"获取目标点时出错: {e}")
            return anchor_point
            
    def _point_to_line_distance(self, point: QPointF, line_start: QPointF, line_end: QPointF) -> float:
        """计算点到线段的最短距离"""
        try:
            # 计算线段长度的平方
            line_length_squared = (line_end.x() - line_start.x())**2 + (line_end.y() - line_start.y())**2
            
            # 如果线段长度为0，则直接返回点到起点的距离
            if line_length_squared == 0:
                return math.sqrt((point.x() - line_start.x())**2 + (point.y() - line_start.y())**2)
            
            # 计算投影比例 t
            t = max(0, min(1, ((point.x() - line_start.x()) * (line_end.x() - line_start.x()) + 
                              (point.y() - line_start.y()) * (line_end.y() - line_start.y())) / 
                            line_length_squared))
            
            # 计算投影点坐标
            projection_x = line_start.x() + t * (line_end.x() - line_start.x())
            projection_y = line_start.y() + t * (line_end.y() - line_start.y())
            
            # 计算点到投影点的距离
            return math.sqrt((point.x() - projection_x)**2 + (point.y() - projection_y)**2)
        except Exception as e:
            print(f"计算点到线距离时出错: {e}")
            return float('inf')
        
    def _draw_arrowhead(self, painter: QPainter, line_start: QPointF, line_end: QPointF):
        angle = math.atan2(-(line_start.y() - line_end.y()), line_start.x() - line_end.x())
        wing_angle = math.pi / 6 
        arrow_p1 = line_end + QPointF(self.arrow_head_size * math.cos(angle - wing_angle), -self.arrow_head_size * math.sin(angle - wing_angle))
        arrow_p2 = line_end + QPointF(self.arrow_head_size * math.cos(angle + wing_angle), -self.arrow_head_size * math.sin(angle + wing_angle))
        painter.drawLine(line_end, arrow_p1)
        painter.drawLine(line_end, arrow_p2)

    def _create_star_polygon(self) -> QPolygonF:
        polygon = QPolygonF()
        # 增加五角星大小系数，原来是1.3，现在增加到1.8
        size_factor = 1.8
        for i in range(5):
            angle_deg = -90 + i * 72; angle_rad = math.radians(angle_deg)
            outer_point = QPointF(self.radius * size_factor * math.cos(angle_rad), self.radius * size_factor * math.sin(angle_rad)); polygon.append(outer_point)
            angle_deg += 36; angle_rad = math.radians(angle_deg)
            inner_point = QPointF(self.radius * 0.4 * size_factor * math.cos(angle_rad), self.radius * 0.4 * size_factor * math.sin(angle_rad)); polygon.append(inner_point)
        return polygon

    def _create_triangle_polygon(self) -> QPolygonF:
        polygon = QPolygonF()
        # 增加三角形大小系数，原来是1.4，现在增加到1.8
        size_factor = 1.8
        for i in range(3):
            angle_deg = -90 + i * 120; angle_rad = math.radians(angle_deg)
            point = QPointF(self.radius * size_factor * math.cos(angle_rad), self.radius * size_factor * math.sin(angle_rad)); polygon.append(point)
        return polygon

    def itemChange(self, change, value):
        if change == QGraphicsObject.ItemPositionChange and self.scene():
            self.prepareGeometryChange()
            self._update_geometry()
            self.moved.emit(self, value)
        return super().itemChange(change, value)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton: super().mousePressEvent(event); self.selected.emit(self)
        elif event.button() == Qt.RightButton: self.show_context_menu(event.screenPos()); event.accept()

    def show_context_menu(self, global_pos):
        menu = QMenu()
        
        delete_action = menu.addAction("删除标注")
        delete_action.triggered.connect(lambda: self.delete_requested.emit(self))
        menu.addSeparator()

        color_action = menu.addAction("选择颜色...")
        color_action.triggered.connect(self._open_color_dialog)
        
        shape_menu = menu.addMenu("更改形状")
        shapes = [("空心圆", "circle"), ("实心圆", "solid_circle"), ("五角星", "pentagram"), ("三角形", "triangle")]
        for shape_name, shape_key in shapes:
            shape_action = shape_menu.addAction(shape_name)
            shape_action.setEnabled(shape_key != self.shape_type)
            if shape_key != self.shape_type:
                shape_action.triggered.connect(lambda checked, s=shape_key: self.change_shape(s))
        
        size_menu = menu.addMenu("调整大小")
        sizes = [("小", 10), ("中", 20), ("大", 25), ("特大", 30), ("自动 (匹配短边)", -1)]
        
        current_size_class = 20  # 默认为"中"
        if self.auto_radius:
            current_size_class = -1
        elif self.scale_factor <= 0.5:
            current_size_class = 10  # 小
        elif self.scale_factor <= 0.7:
            current_size_class = 15  # 小中
        elif self.scale_factor <= 1.0:
            current_size_class = 20  # 中
        elif self.scale_factor <= 1.3:
            current_size_class = 25  # 大
        else:
            current_size_class = 30  # 特大
            
        for size_name, size_val in sizes:
            size_action = size_menu.addAction(size_name)
            size_action.setEnabled(size_val != current_size_class)
            if size_val != current_size_class:
                size_action.triggered.connect(lambda checked, s=size_val: self.change_size(s))
        
        style_menu = menu.addMenu("更改样式")
        styles = [("默认", "default"), ("警告", "warning"), ("错误", "error"), ("成功", "success")]
        for style_name, style_key in styles:
            style_action = style_menu.addAction(style_name)
            style_action.setEnabled(self.style != style_key or self.custom_color is not None)
            if self.style != style_key or self.custom_color is not None:
                style_action.triggered.connect(lambda checked, s=style_key: self.change_style(s))
                
        menu.exec(global_pos.toPoint())

    def _open_color_dialog(self):
        initial_color = self.custom_color if self.custom_color and self.custom_color.isValid() else QColor("blue")
        color = QColorDialog.getColor(initial_color, None, "选择标注颜色")
        if color.isValid():
            self.change_color(color)

    def change_size(self, new_size: int):
        # 调试信息
        print(f"气泡 {self.annotation_id} 调整大小: {new_size}, 自动模式: {self.auto_radius}, 当前比例: {self.scale_factor}")
        
        # 防止递归调用
        is_being_updated = getattr(self, '_is_updating_size', False)
        if is_being_updated:
            print(f"  -> 防止递归: 气泡 {self.annotation_id} 已在更新中")
            return
            
        self._is_updating_size = True
        
        try:
            if new_size == -1:  # 自动大小
                self.auto_radius = True
                # 直接计算半径，而不是调用可能导致递归的方法
                self.radius = max(int(self.base_radius * self.scale_factor), 10)
                print(f"  -> 自动模式设置新半径: {self.radius}，基准半径: {self.base_radius}")
            else:
                # 对于非自动大小，使用比例因子
                self.auto_radius = False
                
                # 新的比例因子计算方式：使用相对比例
                if new_size <= 10:  # 极小
                    self.scale_factor = 0.5
                elif new_size <= 15:  # 小
                    self.scale_factor = 0.7
                elif new_size <= 20:  # 中
                    self.scale_factor = 1.0
                elif new_size <= 25:  # 大
                    self.scale_factor = 1.3
                else:  # 特大
                    self.scale_factor = 1.6
                
                # 直接计算半径，而不是调用方法
                self.radius = max(int(self.base_radius * self.scale_factor), 10)
                print(f"  -> 非自动模式设置新半径: {self.radius}")
            
            # 强制更新几何形状并重绘
            self.prepareGeometryChange()
            self._update_geometry()
            self.update()
            # 发送大小变化信号
            self.size_change_requested.emit(self)
        finally:
            # 确保标志被重置，即使发生异常
            self._is_updating_size = False

    def change_color(self, new_color: QColor):
        self.custom_color = new_color
        self.style = 'custom'
        self.update()
        self.color_change_requested.emit(self)

    def change_shape(self, new_shape: str):
        self.shape_type = new_shape
        self.prepareGeometryChange()
        self._update_geometry()
        self.update()
        self.shape_change_requested.emit(self)

    def change_style(self, new_style: str):
        self.custom_color = None
        self.style = new_style
        self.update()
        self.style_change_requested.emit(self)
    
    def set_highlighted(self, highlighted: bool):
        self._is_highlighted = highlighted
        self.update()
    
    def get_data(self) -> dict:
        color_hex = self.custom_color.name() if self.custom_color and self.custom_color.isValid() else None
        return {
            'id': self.annotation_id, 'text': self.text, 'bubble_position': self.pos(), 
            'anchor_point': self.anchor_point, 'style': self.style, 
            'shape': self.shape_type, 'color': color_hex, 'size': self.radius,
            'dimension': self.dimension, 'dimension_type': self.dimension_type,
            'upper_tolerance': self.upper_tolerance, 'lower_tolerance': self.lower_tolerance,
            'is_audited': self.is_audited, # <-- 新增
            'bbox_points': self.bbox_points, # <-- 添加边界框点信息
            'auto_radius': self.auto_radius, # <-- 添加自动半径标志
            'base_radius': self.base_radius, # <-- 添加基准半径
            'scale_factor': self.scale_factor, # <-- 添加缩放因子
            'edge_attachment_ratio': self.edge_attachment_ratio # <-- 添加边框附着比例
        }

    def set_bbox_points(self, points: list):
        """设置边界框的各个点坐标"""
        try:
            # 添加防抖保护 - 避免过于频繁的更新
            current_time = time.time()
            last_update_time = getattr(self, '_last_bbox_update_time', 0)
            if (current_time - last_update_time) * 1000 < 30:  # 30毫秒内不重复处理
                return
            self._last_bbox_update_time = current_time
            
            # 处理无效输入
            if not points or len(points) < 4:
                self.bbox_points = points
                self.prepareGeometryChange()
                self._update_geometry()
                self.update()
                return
            
            # 首次设置边界框点时，使用默认的attachment_ratio
            if not self.bbox_points or len(self.bbox_points) < 4:
                if not hasattr(self, 'edge_attachment_ratio') or self.edge_attachment_ratio is None:
                    self.edge_attachment_ratio = 0.5
                self.bbox_points = points
                self.prepareGeometryChange()
                self._update_geometry()
                self.update()
                return
                
            # 如果已经有bbox点，计算当前目标点在边上的位置比例
            old_left_x = min(p.x() for p in self.bbox_points)
            old_right_x = max(p.x() for p in self.bbox_points)
            old_top_y = min(p.y() for p in self.bbox_points)
            old_bottom_y = max(p.y() for p in self.bbox_points)
            
            # 气泡位置相对于文本框的位置
            bubble_scene_pos = self.scenePos()
            center_x = sum(p.x() for p in self.bbox_points) / len(self.bbox_points)
            
            # 确保edge_attachment_ratio存在
            if not hasattr(self, 'edge_attachment_ratio') or self.edge_attachment_ratio is None:
                self.edge_attachment_ratio = 0.5
                
            # 更新边界框点
            self.bbox_points = points
            
            # 防止递归调用
            is_being_updated = getattr(self, '_is_updating_size', False)
            if not is_being_updated and self.auto_radius:
                # 直接计算半径，而不是调用_calculate_radius_from_bbox方法
                if not hasattr(self, 'base_radius') or self.base_radius is None:
                    self.base_radius = 20  # 默认基础半径
                if not hasattr(self, 'scale_factor') or self.scale_factor is None:
                    self.scale_factor = 1.0
                self.radius = max(int(self.base_radius * self.scale_factor), 10)
                
            self.prepareGeometryChange()
            self._update_geometry()
            self.update()
        except Exception as e:
            print(f"设置边界框点时出错: {e}")
            # 确保在出错时不会崩溃