# IntelliAnnotate - 智能图纸标注工具

## 简介

IntelliAnnotate 是一个功能完备的2D机械图纸标注应用，支持多种图纸格式加载、智能标注识别、可交互的气泡标注和实时属性编辑。

## 功能特性

- 🖼️ **多格式支持**: 支持PNG、JPG、PDF、DXF文件格式
- 🎯 **智能标注**: 模拟AI识别功能，自动生成标注点
- 🖱️ **交互操作**: 可拖拽的气泡标注，支持实时编辑
- 📝 **属性编辑**: 实时编辑标注文本和属性
- 🔍 **视图控制**: 支持鼠标滚轮缩放和中键拖拽平移
- 📋 **列表管理**: 标注列表显示和快速定位

## 安装步骤

1. 确保已安装Python 3.7+
2. 安装依赖库：
   ```bash
   pip install -r requirements.txt
   ```

## 运行应用

```bash
python run.py
```

或者直接运行主文件：
```bash
python intelliannotate.py
```

## 使用说明

### 1. 加载图纸文件

- 点击菜单栏 `文件 -> 打开...` 或工具栏的 `打开文件` 按钮
- 选择支持的文件格式（PNG、JPG、PDF、DXF）
- 图纸将在中央视图区域显示

### 2. 智能标注识别

- 加载图纸后，点击工具栏的 `AI识别` 按钮
- 系统将模拟AI识别过程，在图纸上随机生成5-10个标注气泡
- 每个气泡包含编号和引线，指向识别的特征点

### 3. 标注管理

**选择标注：**
- 直接点击图纸上的气泡标注
- 或在左侧标注列表中点击对应项目

**编辑标注：**
- 选择标注后，右侧属性编辑器会显示详细信息
- 可在描述框中编辑标注文本，修改会实时保存

**移动标注：**
- 选中标注后直接拖拽到新位置
- 位置信息会在属性编辑器中实时更新

### 4. 视图操作

- **缩放**: 使用鼠标滚轮进行缩放
- **平移**: 按住鼠标中键拖拽平移视图
- **自动定位**: 在标注列表中点击项目会自动居中显示对应标注

### 5. 其他功能

- **清除标注**: 使用工具栏的 `清除标注` 按钮删除所有标注
- **文件格式注意**: 暂不支持DWG格式（程序会提示）

## 界面布局

```
┌─────────────┬─────────────────────────────┬─────────────┐
│             │                             │             │
│   标注列表   │        图纸显示区域          │ 属性编辑器   │
│             │                             │             │
│  - 标注 1   │     [图纸内容]              │  标注ID: 1  │
│  - 标注 2   │     ○─→ ①                  │  位置: ...  │
│  - 标注 3   │     ○─→ ②                  │  描述: ...  │
│    ...      │         ○─→ ③              │             │
│             │                             │             │
└─────────────┴─────────────────────────────┴─────────────┘
```

## 技术架构

- **GUI框架**: PySide6
- **图形系统**: Qt Graphics View Framework
- **图像处理**: Pillow
- **PDF处理**: PyMuPDF
- **DXF处理**: ezdxf

## 系统要求

- Python 3.7+
- Windows/macOS/Linux
- 内存: 建议2GB以上
- 显示: 支持1024x768以上分辨率

## 已知限制

- 暂不支持DWG文件格式
- AI识别为模拟功能，实际生成随机标注
- PDF文件默认显示第一页

## 故障排除

**无法启动应用：**
- 检查Python版本是否为3.7+
- 确认已安装所有依赖库

**文件加载失败：**
- 确认文件格式在支持范围内
- 检查文件是否损坏
- PDF文件确保不是加密文件

**标注显示异常：**
- 尝试清除所有标注后重新识别
- 重新加载图纸文件 