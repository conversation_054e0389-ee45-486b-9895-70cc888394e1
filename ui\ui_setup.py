#!/usr/bin/env python3
"""
UI布局设置模块 - 处理界面设置、菜单栏、工具栏、信号槽连接
"""

import logging
from pathlib import Path

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QLabel, QComboBox, 
    QSlider, QCheckBox, QPushButton, QProgressBar, QSpinBox, QMenuBar,
    QMenu, QStatusBar, QApplication, QMessageBox
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QAction, QIcon, QKeySequence

from ui.graphics_view import GraphicsView
from ui.annotation_list import AnnotationTable
from ui.property_editor import PropertyEditor
from utils.constants import (
    APP_TITLE, DEFAULT_WINDOW_SIZE, DEFAULT_WINDOW_POSITION, PDF_QUALITY_OPTIONS
)

# 获取已配置的logger
logger = logging.getLogger('PyQtBubble.UISetup')

class UISetup:
    """UI设置管理器 - 处理界面布局和组件创建"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
    def setup_ui(self):
        """设置用户界面"""
        # 设置窗口属性
        self.setup_window_properties()
        
        # 创建中央部件和主布局
        self.setup_central_widget()
        
        # 设置菜单栏
        self.setup_menu_bar()
        
        # 设置状态栏
        self.setup_status_bar()
        
        # 设置主要界面组件
        self.setup_main_components()
        
        # 设置信号槽连接
        self.setup_connections()
        
    def setup_window_properties(self):
        """设置窗口属性"""
        self.main_window.setWindowTitle(APP_TITLE)
        self.main_window.setMinimumSize(QSize(1200, 800))
        self.main_window.resize(QSize(*DEFAULT_WINDOW_SIZE))
        
        # 设置窗口图标（如果存在）
        icon_path = Path(__file__).parent.parent / "assets" / "icon.png"
        if icon_path.exists():
            self.main_window.setWindowIcon(QIcon(str(icon_path)))
            
    def setup_central_widget(self):
        """设置中央部件和主布局"""
        # 创建中央部件
        self.main_window.central_widget = QWidget()
        self.main_window.setCentralWidget(self.main_window.central_widget)
        
        # 创建主布局
        self.main_window.main_layout = QHBoxLayout(self.main_window.central_widget)
        self.main_window.main_layout.setContentsMargins(5, 5, 5, 5)
        self.main_window.main_layout.setSpacing(5)
        
        # 创建主分割器
        self.main_window.main_splitter = QSplitter(Qt.Horizontal)
        self.main_window.main_layout.addWidget(self.main_window.main_splitter)
        
    def setup_main_components(self):
        """设置主要界面组件"""
        # 创建左侧面板（图形视图和OCR控制）
        self.setup_left_panel()
        
        # 创建右侧面板（标注列表和属性编辑器）
        self.setup_right_panel()
        
        # 设置分割器比例
        self.main_window.main_splitter.setSizes([800, 400])
        
    def setup_left_panel(self):
        """设置左侧面板（图形视图和OCR控制）"""
        # 创建左侧容器
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(5)
        
        # 创建图形视图
        self.main_window.graphics_view = GraphicsView()
        left_layout.addWidget(self.main_window.graphics_view)
        
        # 获取图形场景
        self.main_window.graphics_scene = self.main_window.graphics_view.scene()
        
        # 设置OCR控制面板
        if hasattr(self.main_window, 'ocr_manager'):
            self.main_window.ocr_manager.setup_compact_ocr_panel(left_layout)
        
        # 添加到主分割器
        self.main_window.main_splitter.addWidget(left_widget)
        
    def setup_right_panel(self):
        """设置右侧面板（标注列表和属性编辑器）"""
        # 创建右侧分割器（垂直）
        right_splitter = QSplitter(Qt.Vertical)
        
        # 创建标注列表面板
        self.setup_annotation_list_panel(right_splitter)
        
        # 创建属性编辑器面板
        self.setup_property_editor_panel(right_splitter)
        
        # 设置右侧分割器比例
        right_splitter.setSizes([300, 200])
        
        # 添加到主分割器
        self.main_window.main_splitter.addWidget(right_splitter)
        
    def setup_annotation_list_panel(self, parent_splitter):
        """设置标注列表面板"""
        # 创建标注列表容器
        annotation_widget = QWidget()
        annotation_layout = QVBoxLayout(annotation_widget)
        annotation_layout.setContentsMargins(5, 5, 5, 5)
        annotation_layout.setSpacing(3)
        
        # 标注列表标题和控制按钮
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("📋 标注列表"))
        
        # 添加标注列表控制按钮
        self.main_window.clear_annotations_btn = QPushButton("清除全部")
        self.main_window.clear_annotations_btn.setMaximumWidth(80)
        header_layout.addWidget(self.main_window.clear_annotations_btn)
        
        self.main_window.reorder_btn = QPushButton("重新排序")
        self.main_window.reorder_btn.setMaximumWidth(80)
        header_layout.addWidget(self.main_window.reorder_btn)
        
        header_layout.addStretch()
        annotation_layout.addLayout(header_layout)
        
        # 创建标注表格
        self.main_window.annotation_table = AnnotationTable()
        annotation_layout.addWidget(self.main_window.annotation_table)
        
        # 添加到分割器
        parent_splitter.addWidget(annotation_widget)
        
    def setup_property_editor_panel(self, parent_splitter):
        """设置属性编辑器面板"""
        # 创建属性编辑器容器
        property_widget = QWidget()
        property_layout = QVBoxLayout(property_widget)
        property_layout.setContentsMargins(5, 5, 5, 5)
        property_layout.setSpacing(3)
        
        # 属性编辑器标题
        property_layout.addWidget(QLabel("⚙️ 属性编辑器"))
        
        # 创建属性编辑器
        self.main_window.property_editor = PropertyEditor()
        property_layout.addWidget(self.main_window.property_editor)
        
        # 添加到分割器
        parent_splitter.addWidget(property_widget)
        
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.main_window.menuBar()
        
        # 文件菜单
        self.setup_file_menu(menubar)
        
        # 编辑菜单
        self.setup_edit_menu(menubar)
        
        # 视图菜单
        self.setup_view_menu(menubar)
        
        # 工具菜单
        self.setup_tools_menu(menubar)
        
        # 帮助菜单
        self.setup_help_menu(menubar)
        
    def setup_file_menu(self, menubar):
        """设置文件菜单"""
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件
        self.main_window.open_action = QAction("打开文件(&O)", self.main_window)
        self.main_window.open_action.setShortcut(QKeySequence.Open)
        self.main_window.open_action.setStatusTip("打开图片或PDF文件")
        file_menu.addAction(self.main_window.open_action)
        
        file_menu.addSeparator()
        
        # 导出子菜单
        export_menu = file_menu.addMenu("导出(&E)")
        
        self.main_window.export_excel_action = QAction("导出到Excel(&X)", self.main_window)
        self.main_window.export_excel_action.setStatusTip("将标注列表导出为Excel文件")
        export_menu.addAction(self.main_window.export_excel_action)
        
        self.main_window.export_template_action = QAction("导出到模板(&T)", self.main_window)
        self.main_window.export_template_action.setStatusTip("将标注列表导出到Excel模板")
        export_menu.addAction(self.main_window.export_template_action)
        
        self.main_window.export_image_action = QAction("导出标注图片(&I)", self.main_window)
        self.main_window.export_image_action.setStatusTip("导出包含标注的图片")
        export_menu.addAction(self.main_window.export_image_action)
        
        file_menu.addSeparator()
        
        # PDF转换
        self.main_window.convert_pdf_action = QAction("PDF转图片(&P)", self.main_window)
        self.main_window.convert_pdf_action.setStatusTip("将PDF文件批量转换为图片")
        file_menu.addAction(self.main_window.convert_pdf_action)
        
        file_menu.addSeparator()
        
        # 退出
        self.main_window.exit_action = QAction("退出(&Q)", self.main_window)
        self.main_window.exit_action.setShortcut(QKeySequence.Quit)
        self.main_window.exit_action.setStatusTip("退出程序")
        file_menu.addAction(self.main_window.exit_action)
        
    def setup_edit_menu(self, menubar):
        """设置编辑菜单"""
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 撤销/重做
        self.main_window.undo_action = QAction("撤销(&U)", self.main_window)
        self.main_window.undo_action.setShortcut(QKeySequence.Undo)
        self.main_window.undo_action.setEnabled(False)
        edit_menu.addAction(self.main_window.undo_action)
        
        self.main_window.redo_action = QAction("重做(&R)", self.main_window)
        self.main_window.redo_action.setShortcut(QKeySequence.Redo)
        self.main_window.redo_action.setEnabled(False)
        edit_menu.addAction(self.main_window.redo_action)
        
        edit_menu.addSeparator()
        
        # 标注操作
        self.main_window.delete_annotation_action = QAction("删除标注(&D)", self.main_window)
        self.main_window.delete_annotation_action.setShortcut(QKeySequence.Delete)
        self.main_window.delete_annotation_action.setStatusTip("删除当前选中的标注")
        edit_menu.addAction(self.main_window.delete_annotation_action)
        
        self.main_window.clear_all_action = QAction("清除所有标注(&C)", self.main_window)
        self.main_window.clear_all_action.setStatusTip("清除所有标注")
        edit_menu.addAction(self.main_window.clear_all_action)
        
        edit_menu.addSeparator()
        
        # 重新排序
        self.main_window.reorder_action = QAction("重新排序(&O)", self.main_window)
        self.main_window.reorder_action.setStatusTip("重新排序所有标注")
        edit_menu.addAction(self.main_window.reorder_action)
        
    def setup_view_menu(self, menubar):
        """设置视图菜单"""
        view_menu = menubar.addMenu("视图(&V)")
        
        # 缩放操作
        self.main_window.zoom_in_action = QAction("放大(&I)", self.main_window)
        self.main_window.zoom_in_action.setShortcut(QKeySequence.ZoomIn)
        view_menu.addAction(self.main_window.zoom_in_action)
        
        self.main_window.zoom_out_action = QAction("缩小(&O)", self.main_window)
        self.main_window.zoom_out_action.setShortcut(QKeySequence.ZoomOut)
        view_menu.addAction(self.main_window.zoom_out_action)
        
        self.main_window.zoom_fit_action = QAction("适应窗口(&F)", self.main_window)
        self.main_window.zoom_fit_action.setShortcut("Ctrl+0")
        view_menu.addAction(self.main_window.zoom_fit_action)
        
        self.main_window.zoom_actual_action = QAction("实际大小(&A)", self.main_window)
        self.main_window.zoom_actual_action.setShortcut("Ctrl+1")
        view_menu.addAction(self.main_window.zoom_actual_action)
        
        view_menu.addSeparator()
        
        # 居中视图
        self.main_window.center_view_action = QAction("居中视图(&C)", self.main_window)
        self.main_window.center_view_action.setShortcut("Ctrl+E")
        view_menu.addAction(self.main_window.center_view_action)
        
    def setup_tools_menu(self, menubar):
        """设置工具菜单"""
        tools_menu = menubar.addMenu("工具(&T)")
        
        # OCR识别
        self.main_window.ocr_action = QAction("OCR识别(&O)", self.main_window)
        self.main_window.ocr_action.setShortcut("F5")
        self.main_window.ocr_action.setStatusTip("开始OCR文字识别")
        tools_menu.addAction(self.main_window.ocr_action)
        
        # 创建标注
        self.main_window.create_all_annotations_action = QAction("创建所有标注(&A)", self.main_window)
        self.main_window.create_all_annotations_action.setStatusTip("从OCR结果创建所有标注")
        tools_menu.addAction(self.main_window.create_all_annotations_action)
        
        tools_menu.addSeparator()
        
        # 审核功能
        self.main_window.audit_action = QAction("审核标注(&R)", self.main_window)
        self.main_window.audit_action.setShortcut("Space")
        self.main_window.audit_action.setStatusTip("审核当前标注并跳转到下一个")
        tools_menu.addAction(self.main_window.audit_action)
        
    def setup_help_menu(self, menubar):
        """设置帮助菜单"""
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        self.main_window.about_action = QAction("关于(&A)", self.main_window)
        self.main_window.about_action.setStatusTip("关于本程序")
        help_menu.addAction(self.main_window.about_action)
        
        # 快捷键帮助
        self.main_window.shortcuts_action = QAction("快捷键(&K)", self.main_window)
        self.main_window.shortcuts_action.setStatusTip("查看快捷键列表")
        help_menu.addAction(self.main_window.shortcuts_action)
        
    def setup_status_bar(self):
        """设置状态栏"""
        self.main_window.status_bar = QStatusBar()
        self.main_window.setStatusBar(self.main_window.status_bar)
        
        # 添加永久状态信息
        self.main_window.status_label = QLabel("就绪")
        self.main_window.status_bar.addWidget(self.main_window.status_label)
        
        # 添加PDF质量控制（放在状态栏右侧）
        self.setup_pdf_quality_controls()
        
    def setup_pdf_quality_controls(self):
        """设置PDF质量控制"""
        # PDF质量选择
        pdf_quality_widget = QWidget()
        pdf_quality_layout = QHBoxLayout(pdf_quality_widget)
        pdf_quality_layout.setContentsMargins(0, 0, 5, 0)
        pdf_quality_layout.setSpacing(5)
        
        pdf_quality_layout.addWidget(QLabel("PDF质量:"))
        self.main_window.pdf_quality_combo = QComboBox()
        self.main_window.pdf_quality_combo.addItems(list(PDF_QUALITY_OPTIONS.keys()))
        self.main_window.pdf_quality_combo.setCurrentText("高清 (4x)")
        self.main_window.pdf_quality_combo.setMaximumWidth(100)
        pdf_quality_layout.addWidget(self.main_window.pdf_quality_combo)
        
        # 强制分辨率选项
        self.main_window.force_resolution_checkbox = QCheckBox("强制分辨率")
        self.main_window.force_resolution_checkbox.setToolTip("强制使用指定分辨率，可能会影响加载速度")
        pdf_quality_layout.addWidget(self.main_window.force_resolution_checkbox)
        
        self.main_window.status_bar.addPermanentWidget(pdf_quality_widget)
        
    def setup_connections(self):
        """设置信号槽连接"""
        # 文件操作连接
        if hasattr(self.main_window, 'file_manager'):
            self.main_window.open_action.triggered.connect(self.main_window.file_manager.open_file)
            self.main_window.export_excel_action.triggered.connect(
                lambda: self.main_window.file_manager.export_to_excel(self.main_window.annotation_manager.annotations)
            )
            self.main_window.export_template_action.triggered.connect(
                lambda: self.main_window.file_manager.export_to_template(self.main_window.annotation_manager.annotations)
            )
            self.main_window.export_image_action.triggered.connect(
                lambda: self.main_window.file_manager.export_annotated_image(
                    self.main_window.current_pixmap, self.main_window.graphics_scene
                )
            )
            self.main_window.convert_pdf_action.triggered.connect(self.main_window.file_manager.convert_pdf_to_images)
        
        # 编辑操作连接
        if hasattr(self.main_window, 'annotation_manager'):
            self.main_window.delete_annotation_action.triggered.connect(
                self.main_window.annotation_manager.delete_current_annotation
            )
            self.main_window.clear_all_action.triggered.connect(
                self.main_window.annotation_manager.clear_annotations
            )
            self.main_window.clear_annotations_btn.clicked.connect(
                self.main_window.annotation_manager.clear_annotations
            )
            self.main_window.reorder_action.triggered.connect(
                self.main_window.annotation_manager.reorder_annotations
            )
            self.main_window.reorder_btn.clicked.connect(
                self.main_window.annotation_manager.reorder_annotations
            )
            self.main_window.audit_action.triggered.connect(
                self.main_window.annotation_manager.audit_current_annotation
            )
        
        # OCR操作连接
        if hasattr(self.main_window, 'ocr_manager'):
            self.main_window.ocr_action.triggered.connect(self.main_window.ocr_manager.start_ocr_recognition)
            self.main_window.create_all_annotations_action.triggered.connect(
                self.main_window.ocr_manager.create_annotations_from_ocr
            )
        
        # 视图操作连接
        if hasattr(self.main_window, 'graphics_view'):
            self.main_window.zoom_in_action.triggered.connect(self.main_window.graphics_view.zoom_in)
            self.main_window.zoom_out_action.triggered.connect(self.main_window.graphics_view.zoom_out)
            self.main_window.zoom_fit_action.triggered.connect(self.main_window.graphics_view.fit_in_view)
            self.main_window.zoom_actual_action.triggered.connect(self.main_window.graphics_view.reset_zoom)
            self.main_window.center_view_action.triggered.connect(self.main_window.center_view)
        
        # 帮助操作连接
        self.main_window.about_action.triggered.connect(self.show_about_dialog)
        self.main_window.shortcuts_action.triggered.connect(self.show_shortcuts_dialog)
        self.main_window.exit_action.triggered.connect(self.main_window.close)
        
        # 标注表格连接
        if hasattr(self.main_window, 'annotation_table'):
            self.main_window.annotation_table.annotation_selected.connect(
                self.main_window.annotation_manager.select_annotation_by_id
            )
        
        # 属性编辑器连接
        if hasattr(self.main_window, 'property_editor'):
            self.main_window.property_editor.text_changed.connect(
                self.main_window.annotation_manager.change_current_annotation_text
            )
            self.main_window.property_editor.style_changed.connect(
                self.main_window.annotation_manager.change_current_annotation_style
            )
            self.main_window.property_editor.shape_changed.connect(
                self.main_window.annotation_manager.change_current_annotation_shape
            )
            self.main_window.property_editor.size_changed.connect(
                self.main_window.annotation_manager.change_annotation_size
            )
        
    def show_about_dialog(self):
        """显示关于对话框"""
        QMessageBox.about(
            self.main_window, "关于",
            f"<h3>{APP_TITLE}</h3>"
            "<p>智能图纸标注工具</p>"
            "<p>版本: 1.0</p>"
            "<p>基于 PySide6 和 PaddleOCR 开发</p>"
            "<p>专为机械制造业设计</p>"
        )
        
    def show_shortcuts_dialog(self):
        """显示快捷键对话框"""
        shortcuts_text = """
        <h3>快捷键列表</h3>
        <table>
        <tr><td><b>Ctrl+O</b></td><td>打开文件</td></tr>
        <tr><td><b>Ctrl+Q</b></td><td>退出程序</td></tr>
        <tr><td><b>Delete</b></td><td>删除选中标注</td></tr>
        <tr><td><b>F5</b></td><td>开始OCR识别</td></tr>
        <tr><td><b>Space</b></td><td>审核标注</td></tr>
        <tr><td><b>Ctrl+Z</b></td><td>撤销</td></tr>
        <tr><td><b>Ctrl+Y</b></td><td>重做</td></tr>
        <tr><td><b>Ctrl++</b></td><td>放大</td></tr>
        <tr><td><b>Ctrl+-</b></td><td>缩小</td></tr>
        <tr><td><b>Ctrl+0</b></td><td>适应窗口</td></tr>
        <tr><td><b>Ctrl+1</b></td><td>实际大小</td></tr>
        <tr><td><b>Ctrl+E</b></td><td>居中视图</td></tr>
        <tr><td><b>左/右方向键</b></td><td>PDF页面切换</td></tr>
        </table>
        """
        QMessageBox.information(self.main_window, "快捷键", shortcuts_text)
