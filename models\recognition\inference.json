{"base_code": {"magic": "pir", "trainable": true, "version": 2}, "program": {"regions": [{"#": "region_0", "blocks": [{"#": "block_0", "args": [], "ops": [{"#": "p", "A": [0, 1, 1, "linear_9.b_0"], "DA": [], "O": {"%": 1, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [254], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_9.w_0"], "DA": [], "O": {"%": 2, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 254], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_85.w_2"], "DA": [], "O": {"%": 3, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_85.w_1"], "DA": [], "O": {"%": 4, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_85.b_0"], "DA": [], "O": {"%": 5, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_85.w_0"], "DA": [], "O": {"%": 6, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_86.w_0"], "DA": [], "O": {"%": 7, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 256, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_84.w_2"], "DA": [], "O": {"%": 8, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_84.w_1"], "DA": [], "O": {"%": 9, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_84.b_0"], "DA": [], "O": {"%": 10, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_84.w_0"], "DA": [], "O": {"%": 11, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_85.w_0"], "DA": [], "O": {"%": 12, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 4096, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.w_2"], "DA": [], "O": {"%": 13, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.w_1"], "DA": [], "O": {"%": 14, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.b_0"], "DA": [], "O": {"%": 15, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.w_0"], "DA": [], "O": {"%": 16, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_84.w_0"], "DA": [], "O": {"%": 17, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048, 120, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_4.b_0"], "DA": [], "O": {"%": 18, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_4.w_0"], "DA": [], "O": {"%": 19, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_8.b_0"], "DA": [], "O": {"%": 20, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_8.w_0"], "DA": [], "O": {"%": 21, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_7.b_0"], "DA": [], "O": {"%": 22, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_7.w_0"], "DA": [], "O": {"%": 23, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_3.b_0"], "DA": [], "O": {"%": 24, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_3.w_0"], "DA": [], "O": {"%": 25, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_6.b_0"], "DA": [], "O": {"%": 26, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_6.w_0"], "DA": [], "O": {"%": 27, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_5.b_0"], "DA": [], "O": {"%": 28, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_5.w_0"], "DA": [], "O": {"%": 29, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_2.b_0"], "DA": [], "O": {"%": 30, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_2.w_0"], "DA": [], "O": {"%": 31, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_4.b_0"], "DA": [], "O": {"%": 32, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_4.w_0"], "DA": [], "O": {"%": 33, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_3.b_0"], "DA": [], "O": {"%": 34, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_3.w_0"], "DA": [], "O": {"%": 35, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_1.b_0"], "DA": [], "O": {"%": 36, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_1.w_0"], "DA": [], "O": {"%": 37, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_2.b_0"], "DA": [], "O": {"%": 38, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_2.w_0"], "DA": [], "O": {"%": 39, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_1.b_0"], "DA": [], "O": {"%": 40, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_1.w_0"], "DA": [], "O": {"%": 41, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_0.b_0"], "DA": [], "O": {"%": 42, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_0.w_0"], "DA": [], "O": {"%": 43, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.w_2"], "DA": [], "O": {"%": 44, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.w_1"], "DA": [], "O": {"%": 45, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.b_0"], "DA": [], "O": {"%": 46, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.w_0"], "DA": [], "O": {"%": 47, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_83.w_0"], "DA": [], "O": {"%": 48, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 256, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.w_2"], "DA": [], "O": {"%": 49, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.w_1"], "DA": [], "O": {"%": 50, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.b_0"], "DA": [], "O": {"%": 51, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.w_0"], "DA": [], "O": {"%": 52, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_82.w_0"], "DA": [], "O": {"%": 53, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 2048, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.w_2"], "DA": [], "O": {"%": 54, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.w_1"], "DA": [], "O": {"%": 55, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.b_0"], "DA": [], "O": {"%": 56, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.w_0"], "DA": [], "O": {"%": 57, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_80.w_0"], "DA": [], "O": {"%": 58, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.w_2"], "DA": [], "O": {"%": 59, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.w_1"], "DA": [], "O": {"%": 60, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.b_0"], "DA": [], "O": {"%": 61, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.w_0"], "DA": [], "O": {"%": 62, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_79.w_0"], "DA": [], "O": {"%": 63, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 3328, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.w_2"], "DA": [], "O": {"%": 64, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.w_1"], "DA": [], "O": {"%": 65, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.b_0"], "DA": [], "O": {"%": 66, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.w_0"], "DA": [], "O": {"%": 67, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_78.w_0"], "DA": [], "O": {"%": 68, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.w_2"], "DA": [], "O": {"%": 69, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.w_1"], "DA": [], "O": {"%": 70, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.b_0"], "DA": [], "O": {"%": 71, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.w_0"], "DA": [], "O": {"%": 72, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_77.w_0"], "DA": [], "O": {"%": 73, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.w_2"], "DA": [], "O": {"%": 74, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.w_1"], "DA": [], "O": {"%": 75, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.b_0"], "DA": [], "O": {"%": 76, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.w_0"], "DA": [], "O": {"%": 77, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_76.w_0"], "DA": [], "O": {"%": 78, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.w_2"], "DA": [], "O": {"%": 79, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.w_1"], "DA": [], "O": {"%": 80, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.b_0"], "DA": [], "O": {"%": 81, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.w_0"], "DA": [], "O": {"%": 82, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_75.w_0"], "DA": [], "O": {"%": 83, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.w_2"], "DA": [], "O": {"%": 84, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.w_1"], "DA": [], "O": {"%": 85, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.b_0"], "DA": [], "O": {"%": 86, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.w_0"], "DA": [], "O": {"%": 87, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_74.w_0"], "DA": [], "O": {"%": 88, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.w_2"], "DA": [], "O": {"%": 89, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.w_1"], "DA": [], "O": {"%": 90, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.b_0"], "DA": [], "O": {"%": 91, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.w_0"], "DA": [], "O": {"%": 92, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_73.w_0"], "DA": [], "O": {"%": 93, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.w_2"], "DA": [], "O": {"%": 94, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.w_1"], "DA": [], "O": {"%": 95, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.b_0"], "DA": [], "O": {"%": 96, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.w_0"], "DA": [], "O": {"%": 97, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_72.w_0"], "DA": [], "O": {"%": 98, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.w_2"], "DA": [], "O": {"%": 99, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.w_1"], "DA": [], "O": {"%": 100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.b_0"], "DA": [], "O": {"%": 101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.w_0"], "DA": [], "O": {"%": 102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_71.w_0"], "DA": [], "O": {"%": 103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.w_2"], "DA": [], "O": {"%": 104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.w_1"], "DA": [], "O": {"%": 105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.b_0"], "DA": [], "O": {"%": 106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.w_0"], "DA": [], "O": {"%": 107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_70.w_0"], "DA": [], "O": {"%": 108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.w_2"], "DA": [], "O": {"%": 109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.w_1"], "DA": [], "O": {"%": 110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.b_0"], "DA": [], "O": {"%": 111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.w_0"], "DA": [], "O": {"%": 112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_69.w_0"], "DA": [], "O": {"%": 113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.w_2"], "DA": [], "O": {"%": 114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.w_1"], "DA": [], "O": {"%": 115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.b_0"], "DA": [], "O": {"%": 116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.w_0"], "DA": [], "O": {"%": 117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_68.w_0"], "DA": [], "O": {"%": 118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.w_2"], "DA": [], "O": {"%": 119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.w_1"], "DA": [], "O": {"%": 120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.b_0"], "DA": [], "O": {"%": 121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.w_0"], "DA": [], "O": {"%": 122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_67.w_0"], "DA": [], "O": {"%": 123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.w_2"], "DA": [], "O": {"%": 124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.w_1"], "DA": [], "O": {"%": 125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.b_0"], "DA": [], "O": {"%": 126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.w_0"], "DA": [], "O": {"%": 127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_66.w_0"], "DA": [], "O": {"%": 128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.w_2"], "DA": [], "O": {"%": 129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.w_1"], "DA": [], "O": {"%": 130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.b_0"], "DA": [], "O": {"%": 131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.w_0"], "DA": [], "O": {"%": 132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_65.w_0"], "DA": [], "O": {"%": 133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.w_2"], "DA": [], "O": {"%": 134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.w_1"], "DA": [], "O": {"%": 135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.b_0"], "DA": [], "O": {"%": 136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.w_0"], "DA": [], "O": {"%": 137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_64.w_0"], "DA": [], "O": {"%": 138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 2176, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.w_2"], "DA": [], "O": {"%": 139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.w_1"], "DA": [], "O": {"%": 140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.b_0"], "DA": [], "O": {"%": 141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.w_0"], "DA": [], "O": {"%": 142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_63.w_0"], "DA": [], "O": {"%": 143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.w_2"], "DA": [], "O": {"%": 144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.w_1"], "DA": [], "O": {"%": 145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.b_0"], "DA": [], "O": {"%": 146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.w_0"], "DA": [], "O": {"%": 147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_62.w_0"], "DA": [], "O": {"%": 148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.w_2"], "DA": [], "O": {"%": 149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.w_1"], "DA": [], "O": {"%": 150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.b_0"], "DA": [], "O": {"%": 151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.w_0"], "DA": [], "O": {"%": 152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_61.w_0"], "DA": [], "O": {"%": 153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.w_2"], "DA": [], "O": {"%": 154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.w_1"], "DA": [], "O": {"%": 155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.b_0"], "DA": [], "O": {"%": 156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.w_0"], "DA": [], "O": {"%": 157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_60.w_0"], "DA": [], "O": {"%": 158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.w_2"], "DA": [], "O": {"%": 159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.w_1"], "DA": [], "O": {"%": 160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.b_0"], "DA": [], "O": {"%": 161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.w_0"], "DA": [], "O": {"%": 162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_59.w_0"], "DA": [], "O": {"%": 163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.w_2"], "DA": [], "O": {"%": 164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.w_1"], "DA": [], "O": {"%": 165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.b_0"], "DA": [], "O": {"%": 166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.w_0"], "DA": [], "O": {"%": 167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_58.w_0"], "DA": [], "O": {"%": 168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.w_2"], "DA": [], "O": {"%": 169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.w_1"], "DA": [], "O": {"%": 170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.b_0"], "DA": [], "O": {"%": 171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.w_0"], "DA": [], "O": {"%": 172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_57.w_0"], "DA": [], "O": {"%": 173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.w_2"], "DA": [], "O": {"%": 174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.w_1"], "DA": [], "O": {"%": 175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.b_0"], "DA": [], "O": {"%": 176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.w_0"], "DA": [], "O": {"%": 177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_56.w_0"], "DA": [], "O": {"%": 178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.w_2"], "DA": [], "O": {"%": 179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.w_1"], "DA": [], "O": {"%": 180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.b_0"], "DA": [], "O": {"%": 181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.w_0"], "DA": [], "O": {"%": 182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_55.w_0"], "DA": [], "O": {"%": 183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.w_2"], "DA": [], "O": {"%": 184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.w_1"], "DA": [], "O": {"%": 185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.b_0"], "DA": [], "O": {"%": 186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.w_0"], "DA": [], "O": {"%": 187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_54.w_0"], "DA": [], "O": {"%": 188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.w_2"], "DA": [], "O": {"%": 189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.w_1"], "DA": [], "O": {"%": 190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.b_0"], "DA": [], "O": {"%": 191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.w_0"], "DA": [], "O": {"%": 192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_53.w_0"], "DA": [], "O": {"%": 193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.w_2"], "DA": [], "O": {"%": 194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.w_1"], "DA": [], "O": {"%": 195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.b_0"], "DA": [], "O": {"%": 196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.w_0"], "DA": [], "O": {"%": 197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_52.w_0"], "DA": [], "O": {"%": 198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.w_2"], "DA": [], "O": {"%": 199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.w_1"], "DA": [], "O": {"%": 200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.b_0"], "DA": [], "O": {"%": 201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.w_0"], "DA": [], "O": {"%": 202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_51.w_0"], "DA": [], "O": {"%": 203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.w_2"], "DA": [], "O": {"%": 204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.w_1"], "DA": [], "O": {"%": 205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.b_0"], "DA": [], "O": {"%": 206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.w_0"], "DA": [], "O": {"%": 207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_50.w_0"], "DA": [], "O": {"%": 208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 2176, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.w_2"], "DA": [], "O": {"%": 209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.w_1"], "DA": [], "O": {"%": 210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.b_0"], "DA": [], "O": {"%": 211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.w_0"], "DA": [], "O": {"%": 212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_49.w_0"], "DA": [], "O": {"%": 213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.w_2"], "DA": [], "O": {"%": 214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.w_1"], "DA": [], "O": {"%": 215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.b_0"], "DA": [], "O": {"%": 216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.w_0"], "DA": [], "O": {"%": 217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_48.w_0"], "DA": [], "O": {"%": 218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.w_2"], "DA": [], "O": {"%": 219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.w_1"], "DA": [], "O": {"%": 220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.b_0"], "DA": [], "O": {"%": 221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.w_0"], "DA": [], "O": {"%": 222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_47.w_0"], "DA": [], "O": {"%": 223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_2"], "DA": [], "O": {"%": 224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_1"], "DA": [], "O": {"%": 225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.b_0"], "DA": [], "O": {"%": 226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_0"], "DA": [], "O": {"%": 227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_46.w_0"], "DA": [], "O": {"%": 228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_2"], "DA": [], "O": {"%": 229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_1"], "DA": [], "O": {"%": 230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.b_0"], "DA": [], "O": {"%": 231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_0"], "DA": [], "O": {"%": 232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_45.w_0"], "DA": [], "O": {"%": 233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_2"], "DA": [], "O": {"%": 234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_1"], "DA": [], "O": {"%": 235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.b_0"], "DA": [], "O": {"%": 236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_0"], "DA": [], "O": {"%": 237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_44.w_0"], "DA": [], "O": {"%": 238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_2"], "DA": [], "O": {"%": 239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_1"], "DA": [], "O": {"%": 240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.b_0"], "DA": [], "O": {"%": 241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_0"], "DA": [], "O": {"%": 242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_43.w_0"], "DA": [], "O": {"%": 243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_2"], "DA": [], "O": {"%": 244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_1"], "DA": [], "O": {"%": 245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.b_0"], "DA": [], "O": {"%": 246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_0"], "DA": [], "O": {"%": 247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_42.w_0"], "DA": [], "O": {"%": 248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_2"], "DA": [], "O": {"%": 249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_1"], "DA": [], "O": {"%": 250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.b_0"], "DA": [], "O": {"%": 251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_0"], "DA": [], "O": {"%": 252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_41.w_0"], "DA": [], "O": {"%": 253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_2"], "DA": [], "O": {"%": 254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_1"], "DA": [], "O": {"%": 255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.b_0"], "DA": [], "O": {"%": 256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_0"], "DA": [], "O": {"%": 257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_40.w_0"], "DA": [], "O": {"%": 258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_2"], "DA": [], "O": {"%": 259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_1"], "DA": [], "O": {"%": 260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.b_0"], "DA": [], "O": {"%": 261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_0"], "DA": [], "O": {"%": 262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_39.w_0"], "DA": [], "O": {"%": 263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_2"], "DA": [], "O": {"%": 264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_1"], "DA": [], "O": {"%": 265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.b_0"], "DA": [], "O": {"%": 266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_0"], "DA": [], "O": {"%": 267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_38.w_0"], "DA": [], "O": {"%": 268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_2"], "DA": [], "O": {"%": 269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_1"], "DA": [], "O": {"%": 270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.b_0"], "DA": [], "O": {"%": 271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_0"], "DA": [], "O": {"%": 272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_37.w_0"], "DA": [], "O": {"%": 273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_2"], "DA": [], "O": {"%": 274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_1"], "DA": [], "O": {"%": 275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.b_0"], "DA": [], "O": {"%": 276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_0"], "DA": [], "O": {"%": 277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_36.w_0"], "DA": [], "O": {"%": 278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 1664, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_2"], "DA": [], "O": {"%": 279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_1"], "DA": [], "O": {"%": 280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.b_0"], "DA": [], "O": {"%": 281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_0"], "DA": [], "O": {"%": 282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_35.w_0"], "DA": [], "O": {"%": 283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_2"], "DA": [], "O": {"%": 284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_1"], "DA": [], "O": {"%": 285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.b_0"], "DA": [], "O": {"%": 286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_0"], "DA": [], "O": {"%": 287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_34.w_0"], "DA": [], "O": {"%": 288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_2"], "DA": [], "O": {"%": 289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_1"], "DA": [], "O": {"%": 290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.b_0"], "DA": [], "O": {"%": 291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_0"], "DA": [], "O": {"%": 292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_33.w_0"], "DA": [], "O": {"%": 293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_2"], "DA": [], "O": {"%": 294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_1"], "DA": [], "O": {"%": 295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.b_0"], "DA": [], "O": {"%": 296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_0"], "DA": [], "O": {"%": 297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_32.w_0"], "DA": [], "O": {"%": 298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_2"], "DA": [], "O": {"%": 299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_1"], "DA": [], "O": {"%": 300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.b_0"], "DA": [], "O": {"%": 301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_0"], "DA": [], "O": {"%": 302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_31.w_0"], "DA": [], "O": {"%": 303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_2"], "DA": [], "O": {"%": 304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_1"], "DA": [], "O": {"%": 305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.b_0"], "DA": [], "O": {"%": 306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_0"], "DA": [], "O": {"%": 307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_30.w_0"], "DA": [], "O": {"%": 308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_2"], "DA": [], "O": {"%": 309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_1"], "DA": [], "O": {"%": 310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.b_0"], "DA": [], "O": {"%": 311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_0"], "DA": [], "O": {"%": 312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.w_0"], "DA": [], "O": {"%": 313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_2"], "DA": [], "O": {"%": 314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_1"], "DA": [], "O": {"%": 315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.b_0"], "DA": [], "O": {"%": 316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_0"], "DA": [], "O": {"%": 317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_28.w_0"], "DA": [], "O": {"%": 318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_2"], "DA": [], "O": {"%": 319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_1"], "DA": [], "O": {"%": 320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.b_0"], "DA": [], "O": {"%": 321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_0"], "DA": [], "O": {"%": 322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_27.w_0"], "DA": [], "O": {"%": 323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_2"], "DA": [], "O": {"%": 324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_1"], "DA": [], "O": {"%": 325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.b_0"], "DA": [], "O": {"%": 326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_0"], "DA": [], "O": {"%": 327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_26.w_0"], "DA": [], "O": {"%": 328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_2"], "DA": [], "O": {"%": 329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_1"], "DA": [], "O": {"%": 330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.b_0"], "DA": [], "O": {"%": 331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_0"], "DA": [], "O": {"%": 332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_25.w_0"], "DA": [], "O": {"%": 333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_2"], "DA": [], "O": {"%": 334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_1"], "DA": [], "O": {"%": 335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.b_0"], "DA": [], "O": {"%": 336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_0"], "DA": [], "O": {"%": 337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_24.w_0"], "DA": [], "O": {"%": 338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_2"], "DA": [], "O": {"%": 339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_1"], "DA": [], "O": {"%": 340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.b_0"], "DA": [], "O": {"%": 341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_0"], "DA": [], "O": {"%": 342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_23.w_0"], "DA": [], "O": {"%": 343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_2"], "DA": [], "O": {"%": 344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_1"], "DA": [], "O": {"%": 345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.b_0"], "DA": [], "O": {"%": 346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_0"], "DA": [], "O": {"%": 347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_22.w_0"], "DA": [], "O": {"%": 348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 256, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_2"], "DA": [], "O": {"%": 349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_1"], "DA": [], "O": {"%": 350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.b_0"], "DA": [], "O": {"%": 351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_0"], "DA": [], "O": {"%": 352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_21.w_0"], "DA": [], "O": {"%": 353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 704, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_2"], "DA": [], "O": {"%": 354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_1"], "DA": [], "O": {"%": 355, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.b_0"], "DA": [], "O": {"%": 356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_0"], "DA": [], "O": {"%": 357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_20.w_0"], "DA": [], "O": {"%": 358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_2"], "DA": [], "O": {"%": 359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_1"], "DA": [], "O": {"%": 360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.b_0"], "DA": [], "O": {"%": 361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_0"], "DA": [], "O": {"%": 362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_19.w_0"], "DA": [], "O": {"%": 363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_2"], "DA": [], "O": {"%": 364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_1"], "DA": [], "O": {"%": 365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.b_0"], "DA": [], "O": {"%": 366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_0"], "DA": [], "O": {"%": 367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_18.w_0"], "DA": [], "O": {"%": 368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_2"], "DA": [], "O": {"%": 369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_1"], "DA": [], "O": {"%": 370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.b_0"], "DA": [], "O": {"%": 371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_0"], "DA": [], "O": {"%": 372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_17.w_0"], "DA": [], "O": {"%": 373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_2"], "DA": [], "O": {"%": 374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_1"], "DA": [], "O": {"%": 375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.b_0"], "DA": [], "O": {"%": 376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_0"], "DA": [], "O": {"%": 377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_16.w_0"], "DA": [], "O": {"%": 378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_2"], "DA": [], "O": {"%": 379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_1"], "DA": [], "O": {"%": 380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.b_0"], "DA": [], "O": {"%": 381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_0"], "DA": [], "O": {"%": 382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_15.w_0"], "DA": [], "O": {"%": 383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_2"], "DA": [], "O": {"%": 384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_1"], "DA": [], "O": {"%": 385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.b_0"], "DA": [], "O": {"%": 386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_0"], "DA": [], "O": {"%": 387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_14.w_0"], "DA": [], "O": {"%": 388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_2"], "DA": [], "O": {"%": 389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_1"], "DA": [], "O": {"%": 390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.b_0"], "DA": [], "O": {"%": 391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_0"], "DA": [], "O": {"%": 392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_13.w_0"], "DA": [], "O": {"%": 393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_2"], "DA": [], "O": {"%": 394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_1"], "DA": [], "O": {"%": 395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.b_0"], "DA": [], "O": {"%": 396, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_0"], "DA": [], "O": {"%": 397, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_12.w_0"], "DA": [], "O": {"%": 398, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 336, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_2"], "DA": [], "O": {"%": 399, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_1"], "DA": [], "O": {"%": 400, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.b_0"], "DA": [], "O": {"%": 401, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_0"], "DA": [], "O": {"%": 402, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_11.w_0"], "DA": [], "O": {"%": 403, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_2"], "DA": [], "O": {"%": 404, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_1"], "DA": [], "O": {"%": 405, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.b_0"], "DA": [], "O": {"%": 406, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_0"], "DA": [], "O": {"%": 407, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_10.w_0"], "DA": [], "O": {"%": 408, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_2"], "DA": [], "O": {"%": 409, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_1"], "DA": [], "O": {"%": 410, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.b_0"], "DA": [], "O": {"%": 411, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_0"], "DA": [], "O": {"%": 412, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_9.w_0"], "DA": [], "O": {"%": 413, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_2"], "DA": [], "O": {"%": 414, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_1"], "DA": [], "O": {"%": 415, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.b_0"], "DA": [], "O": {"%": 416, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_0"], "DA": [], "O": {"%": 417, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_8.w_0"], "DA": [], "O": {"%": 418, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_2"], "DA": [], "O": {"%": 419, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_1"], "DA": [], "O": {"%": 420, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.b_0"], "DA": [], "O": {"%": 421, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_0"], "DA": [], "O": {"%": 422, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_7.w_0"], "DA": [], "O": {"%": 423, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_2"], "DA": [], "O": {"%": 424, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_1"], "DA": [], "O": {"%": 425, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.b_0"], "DA": [], "O": {"%": 426, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_0"], "DA": [], "O": {"%": 427, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_6.w_0"], "DA": [], "O": {"%": 428, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_2"], "DA": [], "O": {"%": 429, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_1"], "DA": [], "O": {"%": 430, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.b_0"], "DA": [], "O": {"%": 431, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_0"], "DA": [], "O": {"%": 432, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_5.w_0"], "DA": [], "O": {"%": 433, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_2"], "DA": [], "O": {"%": 434, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_1"], "DA": [], "O": {"%": 435, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.b_0"], "DA": [], "O": {"%": 436, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_0"], "DA": [], "O": {"%": 437, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_4.w_0"], "DA": [], "O": {"%": 438, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_2"], "DA": [], "O": {"%": 439, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_1"], "DA": [], "O": {"%": 440, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.b_0"], "DA": [], "O": {"%": 441, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_0"], "DA": [], "O": {"%": 442, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_3.w_0"], "DA": [], "O": {"%": 443, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 64, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_2"], "DA": [], "O": {"%": 444, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_1"], "DA": [], "O": {"%": 445, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.b_0"], "DA": [], "O": {"%": 446, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_0"], "DA": [], "O": {"%": 447, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_2.w_0"], "DA": [], "O": {"%": 448, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 16, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_2"], "DA": [], "O": {"%": 449, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_1"], "DA": [], "O": {"%": 450, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.b_0"], "DA": [], "O": {"%": 451, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_0"], "DA": [], "O": {"%": 452, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_1.w_0"], "DA": [], "O": {"%": 453, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 32, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_2"], "DA": [], "O": {"%": 454, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_1"], "DA": [], "O": {"%": 455, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.b_0"], "DA": [], "O": {"%": 456, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_0"], "DA": [], "O": {"%": 457, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_0.w_0"], "DA": [], "O": {"%": 458, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 3, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "1.data", "A": [{"AT": {"#": "0.a_str", "D": "x"}, "N": "name"}, {"AT": {"#": "1.a_intarray", "D": [-1, 3, 48, -1]}, "N": "shape"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [0, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 459, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, 48, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 459}, {"%": 458}], "O": [{"%": 460, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 460}, {"%": 455}, {"%": 454}, {"%": 457}, {"%": 456}], "O": [{"%": 461, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}, {"%": 462, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 463, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 464, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 465, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 466, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 461}], "O": [{"%": 467, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "SAME"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 467}, {"%": 453}], "O": [{"%": 468, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 468}, {"%": 450}, {"%": 449}, {"%": 452}, {"%": 451}], "O": [{"%": 469, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}, {"%": 470, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 471, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 472, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 473, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 474, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 469}], "O": [{"%": 475, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "SAME"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 475}, {"%": 448}], "O": [{"%": 476, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 476}, {"%": 445}, {"%": 444}, {"%": 447}, {"%": 446}], "O": [{"%": 477, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}, {"%": 478, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 479, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 480, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 481, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 482, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 477}], "O": [{"%": 483, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/MaxPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 484, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "max"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "SAME"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/MaxPool2D/"}, "N": "struct_name"}], "I": [{"%": 467}, {"%": 484}], "O": [{"%": 485, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/"}, "N": "struct_name"}], "I": [], "O": [{"%": 486, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/"}, "N": "struct_name"}], "I": [{"%": 485}, {"%": 483}], "O": [{"%": 487, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/"}, "N": "struct_name"}], "I": [{"%": 487}, {"%": 486}], "O": [{"%": 488, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 488}, {"%": 443}], "O": [{"%": 489, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 489}, {"%": 440}, {"%": 439}, {"%": 442}, {"%": 441}], "O": [{"%": 490, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}, {"%": 491, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 492, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 493, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 494, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 495, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 490}], "O": [{"%": 496, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 496}, {"%": 438}], "O": [{"%": 497, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 497}, {"%": 435}, {"%": 434}, {"%": 437}, {"%": 436}], "O": [{"%": 498, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 24, -1], "NCHW", [], 0]}}, {"%": 499, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 500, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 501, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 502, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 503, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 498}], "O": [{"%": 504, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 48}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 504}, {"%": 433}], "O": [{"%": 505, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 505}, {"%": 430}, {"%": 429}, {"%": 432}, {"%": 431}], "O": [{"%": 506, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}, {"%": 507, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 508, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 509, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 510, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 511, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 506}, {"%": 428}], "O": [{"%": 512, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 512}, {"%": 425}, {"%": 424}, {"%": 427}, {"%": 426}], "O": [{"%": 513, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}, {"%": 514, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 515, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 516, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 517, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 518, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 513}], "O": [{"%": 519, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 519}, {"%": 423}], "O": [{"%": 520, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 520}, {"%": 420}, {"%": 419}, {"%": 422}, {"%": 421}], "O": [{"%": 521, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}, {"%": 522, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 523, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 524, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 525, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 526, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 521}], "O": [{"%": 527, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 527}, {"%": 418}], "O": [{"%": 528, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 528}, {"%": 415}, {"%": 414}, {"%": 417}, {"%": 416}], "O": [{"%": 529, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}, {"%": 530, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 531, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 532, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 533, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 534, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 529}], "O": [{"%": 535, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 535}, {"%": 413}], "O": [{"%": 536, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 536}, {"%": 410}, {"%": 409}, {"%": 412}, {"%": 411}], "O": [{"%": 537, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}, {"%": 538, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 539, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 540, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 541, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 542, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 537}], "O": [{"%": 543, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 543}, {"%": 408}], "O": [{"%": 544, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 544}, {"%": 405}, {"%": 404}, {"%": 407}, {"%": 406}], "O": [{"%": 545, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}, {"%": 546, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 547, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 548, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 549, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 550, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 545}], "O": [{"%": 551, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 551}, {"%": 403}], "O": [{"%": 552, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 552}, {"%": 400}, {"%": 399}, {"%": 402}, {"%": 401}], "O": [{"%": 553, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}, {"%": 554, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 555, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 556, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 557, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 558, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 553}], "O": [{"%": 559, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 560, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 506}, {"%": 519}, {"%": 527}, {"%": 535}, {"%": 543}, {"%": 551}, {"%": 559}], "O": [{"%": 561, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 12, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 561}, {"%": 560}], "O": [{"%": 562, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 336, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 562}, {"%": 398}], "O": [{"%": 563, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 563}, {"%": 395}, {"%": 394}, {"%": 397}, {"%": 396}], "O": [{"%": 564, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}, {"%": 565, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 566, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 567, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 568, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 569, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 564}], "O": [{"%": 570, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_7/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 570}, {"%": 393}], "O": [{"%": 571, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_7/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 571}, {"%": 390}, {"%": 389}, {"%": 392}, {"%": 391}], "O": [{"%": 572, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 573, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 574, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 575, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 576, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 577, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_7/ReLU/"}, "N": "struct_name"}], "I": [{"%": 572}], "O": [{"%": 578, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 128}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 578}, {"%": 388}], "O": [{"%": 579, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 579}, {"%": 385}, {"%": 384}, {"%": 387}, {"%": 386}], "O": [{"%": 580, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 581, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 582, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 583, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 584, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 585, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 580}, {"%": 383}], "O": [{"%": 586, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 586}, {"%": 380}, {"%": 379}, {"%": 382}, {"%": 381}], "O": [{"%": 587, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}, {"%": 588, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 589, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 590, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 591, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 592, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 587}], "O": [{"%": 593, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 593}, {"%": 378}], "O": [{"%": 594, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 594}, {"%": 375}, {"%": 374}, {"%": 377}, {"%": 376}], "O": [{"%": 595, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}, {"%": 596, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 597, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 598, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 599, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 600, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 595}], "O": [{"%": 601, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 601}, {"%": 373}], "O": [{"%": 602, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 602}, {"%": 370}, {"%": 369}, {"%": 372}, {"%": 371}], "O": [{"%": 603, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}, {"%": 604, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 605, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 606, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 607, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 608, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 603}], "O": [{"%": 609, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 609}, {"%": 368}], "O": [{"%": 610, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 610}, {"%": 365}, {"%": 364}, {"%": 367}, {"%": 366}], "O": [{"%": 611, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}, {"%": 612, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 613, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 614, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 615, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 616, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 611}], "O": [{"%": 617, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 617}, {"%": 363}], "O": [{"%": 618, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 618}, {"%": 360}, {"%": 359}, {"%": 362}, {"%": 361}], "O": [{"%": 619, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}, {"%": 620, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 621, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 622, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 623, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 624, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 619}], "O": [{"%": 625, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 625}, {"%": 358}], "O": [{"%": 626, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 626}, {"%": 355}, {"%": 354}, {"%": 357}, {"%": 356}], "O": [{"%": 627, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}, {"%": 628, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 629, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 630, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 631, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 632, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 627}], "O": [{"%": 633, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 634, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 580}, {"%": 593}, {"%": 601}, {"%": 609}, {"%": 617}, {"%": 625}, {"%": 633}], "O": [{"%": 635, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 12, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 635}, {"%": 634}], "O": [{"%": 636, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 704, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 353}], "O": [{"%": 637, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 637}, {"%": 350}, {"%": 349}, {"%": 352}, {"%": 351}], "O": [{"%": 638, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}, {"%": 639, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 640, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 641, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 642, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 643, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 638}], "O": [{"%": 644, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_7/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 644}, {"%": 348}], "O": [{"%": 645, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_7/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 645}, {"%": 345}, {"%": 344}, {"%": 347}, {"%": 346}], "O": [{"%": 646, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 12, -1], "NCHW", [], 0]}}, {"%": 647, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 648, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 649, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 650, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 651, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_7/ReLU/"}, "N": "struct_name"}], "I": [{"%": 646}], "O": [{"%": 652, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 512}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 652}, {"%": 343}], "O": [{"%": 653, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 653}, {"%": 340}, {"%": 339}, {"%": 342}, {"%": 341}], "O": [{"%": 654, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}, {"%": 655, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 656, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 657, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 658, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 659, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 654}, {"%": 338}], "O": [{"%": 660, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 660}, {"%": 335}, {"%": 334}, {"%": 337}, {"%": 336}], "O": [{"%": 661, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 662, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 663, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 664, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 665, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 666, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 661}, {"%": 333}], "O": [{"%": 667, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 667}, {"%": 330}, {"%": 329}, {"%": 332}, {"%": 331}], "O": [{"%": 668, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 669, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 670, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 671, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 672, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 673, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 668}], "O": [{"%": 674, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 674}, {"%": 328}], "O": [{"%": 675, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 675}, {"%": 325}, {"%": 324}, {"%": 327}, {"%": 326}], "O": [{"%": 676, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 677, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 678, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 679, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 680, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 681, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 676}, {"%": 323}], "O": [{"%": 682, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 682}, {"%": 320}, {"%": 319}, {"%": 322}, {"%": 321}], "O": [{"%": 683, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 684, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 685, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 686, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 687, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 688, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 683}], "O": [{"%": 689, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 689}, {"%": 318}], "O": [{"%": 690, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 690}, {"%": 315}, {"%": 314}, {"%": 317}, {"%": 316}], "O": [{"%": 691, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 692, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 693, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 694, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 695, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 696, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 691}, {"%": 313}], "O": [{"%": 697, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 697}, {"%": 310}, {"%": 309}, {"%": 312}, {"%": 311}], "O": [{"%": 698, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 699, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 700, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 701, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 702, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 703, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 698}], "O": [{"%": 704, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 704}, {"%": 308}], "O": [{"%": 705, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 705}, {"%": 305}, {"%": 304}, {"%": 307}, {"%": 306}], "O": [{"%": 706, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 707, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 708, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 709, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 710, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 711, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 706}, {"%": 303}], "O": [{"%": 712, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 712}, {"%": 300}, {"%": 299}, {"%": 302}, {"%": 301}], "O": [{"%": 713, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 714, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 715, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 716, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 717, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 718, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 713}], "O": [{"%": 719, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 719}, {"%": 298}], "O": [{"%": 720, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 720}, {"%": 295}, {"%": 294}, {"%": 297}, {"%": 296}], "O": [{"%": 721, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 722, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 723, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 724, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 725, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 726, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 721}, {"%": 293}], "O": [{"%": 727, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 727}, {"%": 290}, {"%": 289}, {"%": 292}, {"%": 291}], "O": [{"%": 728, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 729, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 730, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 731, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 732, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 733, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 728}], "O": [{"%": 734, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 734}, {"%": 288}], "O": [{"%": 735, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 735}, {"%": 285}, {"%": 284}, {"%": 287}, {"%": 286}], "O": [{"%": 736, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 737, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 738, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 739, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 740, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 741, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 736}, {"%": 283}], "O": [{"%": 742, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 742}, {"%": 280}, {"%": 279}, {"%": 282}, {"%": 281}], "O": [{"%": 743, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 744, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 745, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 746, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 747, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 748, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 743}], "O": [{"%": 749, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 750, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 654}, {"%": 674}, {"%": 689}, {"%": 704}, {"%": 719}, {"%": 734}, {"%": 749}], "O": [{"%": 751, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 751}, {"%": 750}], "O": [{"%": 752, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1664, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 752}, {"%": 278}], "O": [{"%": 753, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 753}, {"%": 275}, {"%": 274}, {"%": 277}, {"%": 276}], "O": [{"%": 754, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}, {"%": 755, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 756, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 757, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 758, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 759, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 754}], "O": [{"%": 760, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 760}, {"%": 273}], "O": [{"%": 761, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 761}, {"%": 270}, {"%": 269}, {"%": 272}, {"%": 271}], "O": [{"%": 762, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}, {"%": 763, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 764, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 765, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 766, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 767, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 762}], "O": [{"%": 768, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 768}, {"%": 268}], "O": [{"%": 769, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 769}, {"%": 265}, {"%": 264}, {"%": 267}, {"%": 266}], "O": [{"%": 770, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 771, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 772, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 773, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 774, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 775, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 770}, {"%": 263}], "O": [{"%": 776, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 776}, {"%": 260}, {"%": 259}, {"%": 262}, {"%": 261}], "O": [{"%": 777, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 778, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 779, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 780, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 781, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 782, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 777}], "O": [{"%": 783, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 783}, {"%": 258}], "O": [{"%": 784, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 784}, {"%": 255}, {"%": 254}, {"%": 257}, {"%": 256}], "O": [{"%": 785, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 786, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 787, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 788, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 789, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 790, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 785}, {"%": 253}], "O": [{"%": 791, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 791}, {"%": 250}, {"%": 249}, {"%": 252}, {"%": 251}], "O": [{"%": 792, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 793, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 794, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 795, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 796, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 797, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 792}], "O": [{"%": 798, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 798}, {"%": 248}], "O": [{"%": 799, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 799}, {"%": 245}, {"%": 244}, {"%": 247}, {"%": 246}], "O": [{"%": 800, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 801, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 802, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 803, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 804, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 805, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 800}, {"%": 243}], "O": [{"%": 806, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 806}, {"%": 240}, {"%": 239}, {"%": 242}, {"%": 241}], "O": [{"%": 807, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 808, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 809, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 810, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 811, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 812, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 807}], "O": [{"%": 813, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 813}, {"%": 238}], "O": [{"%": 814, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 814}, {"%": 235}, {"%": 234}, {"%": 237}, {"%": 236}], "O": [{"%": 815, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 816, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 817, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 818, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 819, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 820, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 815}, {"%": 233}], "O": [{"%": 821, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 821}, {"%": 230}, {"%": 229}, {"%": 232}, {"%": 231}], "O": [{"%": 822, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 823, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 824, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 825, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 826, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 827, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 822}], "O": [{"%": 828, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 828}, {"%": 228}], "O": [{"%": 829, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 829}, {"%": 225}, {"%": 224}, {"%": 227}, {"%": 226}], "O": [{"%": 830, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 831, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 832, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 833, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 834, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 835, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 830}, {"%": 223}], "O": [{"%": 836, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 836}, {"%": 220}, {"%": 219}, {"%": 222}, {"%": 221}], "O": [{"%": 837, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 838, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 839, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 840, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 841, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 842, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 837}], "O": [{"%": 843, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 843}, {"%": 218}], "O": [{"%": 844, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 844}, {"%": 215}, {"%": 214}, {"%": 217}, {"%": 216}], "O": [{"%": 845, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 846, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 847, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 848, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 849, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 850, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 845}, {"%": 213}], "O": [{"%": 851, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 851}, {"%": 210}, {"%": 209}, {"%": 212}, {"%": 211}], "O": [{"%": 852, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 853, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 854, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 855, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 856, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 857, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 852}], "O": [{"%": 858, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 859, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [{"%": 768}, {"%": 783}, {"%": 798}, {"%": 813}, {"%": 828}, {"%": 843}, {"%": 858}], "O": [{"%": 860, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [{"%": 860}, {"%": 859}], "O": [{"%": 861, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2176, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 861}, {"%": 208}], "O": [{"%": 862, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 862}, {"%": 205}, {"%": 204}, {"%": 207}, {"%": 206}], "O": [{"%": 863, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}, {"%": 864, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 865, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 866, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 867, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 868, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 863}], "O": [{"%": 869, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 869}, {"%": 203}], "O": [{"%": 870, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 870}, {"%": 200}, {"%": 199}, {"%": 202}, {"%": 201}], "O": [{"%": 871, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}, {"%": 872, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 873, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 874, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 875, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 876, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 871}], "O": [{"%": 877, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [{"%": 877}, {"%": 768}], "O": [{"%": 878, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 878}, {"%": 198}], "O": [{"%": 879, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 879}, {"%": 195}, {"%": 194}, {"%": 197}, {"%": 196}], "O": [{"%": 880, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 881, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 882, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 883, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 884, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 885, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 880}, {"%": 193}], "O": [{"%": 886, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 886}, {"%": 190}, {"%": 189}, {"%": 192}, {"%": 191}], "O": [{"%": 887, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 888, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 889, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 890, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 891, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 892, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 887}], "O": [{"%": 893, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 893}, {"%": 188}], "O": [{"%": 894, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 894}, {"%": 185}, {"%": 184}, {"%": 187}, {"%": 186}], "O": [{"%": 895, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 896, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 897, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 898, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 899, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 900, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 895}, {"%": 183}], "O": [{"%": 901, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 901}, {"%": 180}, {"%": 179}, {"%": 182}, {"%": 181}], "O": [{"%": 902, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 903, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 904, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 905, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 906, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 907, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 902}], "O": [{"%": 908, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 908}, {"%": 178}], "O": [{"%": 909, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 909}, {"%": 175}, {"%": 174}, {"%": 177}, {"%": 176}], "O": [{"%": 910, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 911, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 912, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 913, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 914, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 915, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 910}, {"%": 173}], "O": [{"%": 916, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 916}, {"%": 170}, {"%": 169}, {"%": 172}, {"%": 171}], "O": [{"%": 917, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 918, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 919, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 920, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 921, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 922, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 917}], "O": [{"%": 923, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 923}, {"%": 168}], "O": [{"%": 924, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 924}, {"%": 165}, {"%": 164}, {"%": 167}, {"%": 166}], "O": [{"%": 925, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 926, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 927, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 928, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 929, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 930, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 925}, {"%": 163}], "O": [{"%": 931, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 931}, {"%": 160}, {"%": 159}, {"%": 162}, {"%": 161}], "O": [{"%": 932, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 933, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 934, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 935, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 936, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 937, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 932}], "O": [{"%": 938, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 938}, {"%": 158}], "O": [{"%": 939, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 939}, {"%": 155}, {"%": 154}, {"%": 157}, {"%": 156}], "O": [{"%": 940, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 941, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 942, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 943, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 944, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 945, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 940}, {"%": 153}], "O": [{"%": 946, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 946}, {"%": 150}, {"%": 149}, {"%": 152}, {"%": 151}], "O": [{"%": 947, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 948, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 949, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 950, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 951, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 952, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 947}], "O": [{"%": 953, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 953}, {"%": 148}], "O": [{"%": 954, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 954}, {"%": 145}, {"%": 144}, {"%": 147}, {"%": 146}], "O": [{"%": 955, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 956, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 957, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 958, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 959, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 960, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 955}, {"%": 143}], "O": [{"%": 961, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 961}, {"%": 140}, {"%": 139}, {"%": 142}, {"%": 141}], "O": [{"%": 962, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 963, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 964, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 965, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 966, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 967, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 962}], "O": [{"%": 968, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 969, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [{"%": 878}, {"%": 893}, {"%": 908}, {"%": 923}, {"%": 938}, {"%": 953}, {"%": 968}], "O": [{"%": 970, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [{"%": 970}, {"%": 969}], "O": [{"%": 971, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2176, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 971}, {"%": 138}], "O": [{"%": 972, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 972}, {"%": 135}, {"%": 134}, {"%": 137}, {"%": 136}], "O": [{"%": 973, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}, {"%": 974, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 975, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 976, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 977, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 978, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 973}], "O": [{"%": 979, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 979}, {"%": 133}], "O": [{"%": 980, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 980}, {"%": 130}, {"%": 129}, {"%": 132}, {"%": 131}], "O": [{"%": 981, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}, {"%": 982, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 983, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 984, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 985, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 986, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 981}], "O": [{"%": 987, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [{"%": 987}, {"%": 878}], "O": [{"%": 988, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 1024}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 988}, {"%": 128}], "O": [{"%": 989, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 989}, {"%": 125}, {"%": 124}, {"%": 127}, {"%": 126}], "O": [{"%": 990, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}, {"%": 991, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 992, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 993, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 994, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 995, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 990}, {"%": 123}], "O": [{"%": 996, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 996}, {"%": 120}, {"%": 119}, {"%": 122}, {"%": 121}], "O": [{"%": 997, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 998, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 999, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1000, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1001, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1002, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 997}, {"%": 118}], "O": [{"%": 1003, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1003}, {"%": 115}, {"%": 114}, {"%": 117}, {"%": 116}], "O": [{"%": 1004, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1005, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1006, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1007, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1008, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1009, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1004}], "O": [{"%": 1010, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1010}, {"%": 113}], "O": [{"%": 1011, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1011}, {"%": 110}, {"%": 109}, {"%": 112}, {"%": 111}], "O": [{"%": 1012, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1013, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1014, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1015, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1016, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1017, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1012}, {"%": 108}], "O": [{"%": 1018, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1018}, {"%": 105}, {"%": 104}, {"%": 107}, {"%": 106}], "O": [{"%": 1019, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1020, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1021, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1022, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1023, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1024, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1019}], "O": [{"%": 1025, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1025}, {"%": 103}], "O": [{"%": 1026, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1026}, {"%": 100}, {"%": 99}, {"%": 102}, {"%": 101}], "O": [{"%": 1027, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1028, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1029, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1030, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1031, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1032, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1027}, {"%": 98}], "O": [{"%": 1033, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1033}, {"%": 95}, {"%": 94}, {"%": 97}, {"%": 96}], "O": [{"%": 1034, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1035, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1036, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1037, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1038, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1039, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1034}], "O": [{"%": 1040, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1040}, {"%": 93}], "O": [{"%": 1041, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1041}, {"%": 90}, {"%": 89}, {"%": 92}, {"%": 91}], "O": [{"%": 1042, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1043, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1044, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1045, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1046, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1047, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1042}, {"%": 88}], "O": [{"%": 1048, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1048}, {"%": 85}, {"%": 84}, {"%": 87}, {"%": 86}], "O": [{"%": 1049, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1050, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1051, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1052, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1053, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1054, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1049}], "O": [{"%": 1055, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1055}, {"%": 83}], "O": [{"%": 1056, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1056}, {"%": 80}, {"%": 79}, {"%": 82}, {"%": 81}], "O": [{"%": 1057, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1058, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1059, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1060, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1061, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1062, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1057}, {"%": 78}], "O": [{"%": 1063, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1063}, {"%": 75}, {"%": 74}, {"%": 77}, {"%": 76}], "O": [{"%": 1064, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1065, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1066, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1067, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1068, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1069, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1064}], "O": [{"%": 1070, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1070}, {"%": 73}], "O": [{"%": 1071, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1071}, {"%": 70}, {"%": 69}, {"%": 72}, {"%": 71}], "O": [{"%": 1072, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1073, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1074, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1075, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1076, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1077, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1072}, {"%": 68}], "O": [{"%": 1078, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1078}, {"%": 65}, {"%": 64}, {"%": 67}, {"%": 66}], "O": [{"%": 1079, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}, {"%": 1080, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1081, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1082, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1083, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1084, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1079}], "O": [{"%": 1085, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1086, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 990}, {"%": 1010}, {"%": 1025}, {"%": 1040}, {"%": 1055}, {"%": 1070}, {"%": 1085}], "O": [{"%": 1087, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 3, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 1087}, {"%": 1086}], "O": [{"%": 1088, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3328, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1088}, {"%": 63}], "O": [{"%": 1089, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1089}, {"%": 60}, {"%": 59}, {"%": 62}, {"%": 61}], "O": [{"%": 1090, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}, {"%": 1091, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1092, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1093, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1094, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1095, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1090}], "O": [{"%": 1096, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1096}, {"%": 58}], "O": [{"%": 1097, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1097}, {"%": 55}, {"%": 54}, {"%": 57}, {"%": 56}], "O": [{"%": 1098, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 3, -1], "NCHW", [], 0]}}, {"%": 1099, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1098}], "O": [{"%": 1104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/"}, "N": "struct_name"}], "I": [{"%": 1104}, {"%": 1105}], "O": [{"%": 1106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.assign", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1106}], "O": [{"%": 1107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1107}, {"%": 53}], "O": [{"%": 1108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1108}, {"%": 50}, {"%": 49}, {"%": 52}, {"%": 51}], "O": [{"%": 1109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, -1], "NCHW", [], 0]}}, {"%": 1110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/Swish/"}, "N": "struct_name"}], "I": [{"%": 1109}], "O": [{"%": 1115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1115}, {"%": 48}], "O": [{"%": 1116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1116}, {"%": 45}, {"%": 44}, {"%": 47}, {"%": 46}], "O": [{"%": 1117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}, {"%": 1118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/Swish/"}, "N": "struct_name"}], "I": [{"%": 1117}], "O": [{"%": 1123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.shape64", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1123}], "O": [{"%": 1124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 4}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1124}, {"%": 1125}, {"%": 1126}], "O": [{"%": 1127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.flatten", "A": [{"AT": {"#": "0.a_i32", "D": 2}, "N": "start_axis"}, {"AT": {"#": "0.a_i32", "D": 3}, "N": "stop_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1123}], "O": [{"%": 1128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1128}], "O": [{"%": 1129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 1129}, {"%": 43}, {"%": 42}], "O": [{"%": 1130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 1132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 1130}, {"%": 41}], "O": [{"%": 1133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 1133}, {"%": 40}], "O": [{"%": 1134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 8}, {"#": "0.a_i64", "D": 15}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [5], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1134}, {"%": 1135}], "O": [{"%": 1136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 3, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 4}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1136}], "O": [{"%": 1137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3, -1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1137}, {"%": 1138}, {"%": 1139}], "O": [{"%": 1140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.25819888710975647}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.scale", "A": [{"AT": {"#": "0.a_f32", "D": 0.0}, "N": "bias"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "bias_after_scale"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1140}, {"%": 1141}], "O": [{"%": 1142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1137}, {"%": 1143}, {"%": 1144}], "O": [{"%": 1145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1137}, {"%": 1146}, {"%": 1147}], "O": [{"%": 1148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1145}], "O": [{"%": 1149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 15, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1142}, {"%": 1149}], "O": [{"%": 1150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": -1}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1150}], "O": [{"%": 1151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout/"}, "N": "struct_name"}], "I": [{"%": 1151}, {"%": 0}, {"%": 1152}], "O": [{"%": 1153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}, {"%": 1154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1153}, {"%": 1148}], "O": [{"%": 1155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1155}], "O": [{"%": 1156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 120}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [3], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 1156}, {"%": 1157}], "O": [{"%": 1158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1158}, {"%": 39}], "O": [{"%": 1159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1159}, {"%": 38}], "O": [{"%": 1160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 1160}, {"%": 0}, {"%": 1161}], "O": [{"%": 1162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/"}, "N": "struct_name"}], "I": [{"%": 1129}, {"%": 1162}], "O": [{"%": 1164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/LayerNorm_1/"}, "N": "struct_name"}], "I": [{"%": 1164}, {"%": 37}, {"%": 36}], "O": [{"%": 1165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 1167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 1165}, {"%": 35}], "O": [{"%": 1168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 1168}, {"%": 34}], "O": [{"%": 1169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Swish/"}, "N": "struct_name"}], "I": [{"%": 1169}], "O": [{"%": 1170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout/"}, "N": "struct_name"}], "I": [{"%": 1170}, {"%": 0}, {"%": 1171}], "O": [{"%": 1172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}, {"%": 1173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1172}, {"%": 33}], "O": [{"%": 1174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1174}, {"%": 32}], "O": [{"%": 1175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 1175}, {"%": 0}, {"%": 1176}], "O": [{"%": 1177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/"}, "N": "struct_name"}], "I": [{"%": 1164}, {"%": 1177}], "O": [{"%": 1179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 1179}, {"%": 31}, {"%": 30}], "O": [{"%": 1180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 1182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 1180}, {"%": 29}], "O": [{"%": 1183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 1183}, {"%": 28}], "O": [{"%": 1184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 8}, {"#": "0.a_i64", "D": 15}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [5], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1184}, {"%": 1185}], "O": [{"%": 1186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 3, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 4}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1186}], "O": [{"%": 1187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3, -1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1187}, {"%": 1188}, {"%": 1189}], "O": [{"%": 1190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.25819888710975647}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.scale", "A": [{"AT": {"#": "0.a_f32", "D": 0.0}, "N": "bias"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "bias_after_scale"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1190}, {"%": 1191}], "O": [{"%": 1192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1187}, {"%": 1193}, {"%": 1194}], "O": [{"%": 1195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1187}, {"%": 1196}, {"%": 1197}], "O": [{"%": 1198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1195}], "O": [{"%": 1199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 15, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1192}, {"%": 1199}], "O": [{"%": 1200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": -1}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1200}], "O": [{"%": 1201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout/"}, "N": "struct_name"}], "I": [{"%": 1201}, {"%": 0}, {"%": 1202}], "O": [{"%": 1203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}, {"%": 1204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1203}, {"%": 1198}], "O": [{"%": 1205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1205}], "O": [{"%": 1206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 120}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [3], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 1206}, {"%": 1207}], "O": [{"%": 1208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1208}, {"%": 27}], "O": [{"%": 1209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1209}, {"%": 26}], "O": [{"%": 1210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 1210}, {"%": 0}, {"%": 1211}], "O": [{"%": 1212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/"}, "N": "struct_name"}], "I": [{"%": 1179}, {"%": 1212}], "O": [{"%": 1214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/LayerNorm_1/"}, "N": "struct_name"}], "I": [{"%": 1214}, {"%": 25}, {"%": 24}], "O": [{"%": 1215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 1217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 1215}, {"%": 23}], "O": [{"%": 1218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 1218}, {"%": 22}], "O": [{"%": 1219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Swish/"}, "N": "struct_name"}], "I": [{"%": 1219}], "O": [{"%": 1220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout/"}, "N": "struct_name"}], "I": [{"%": 1220}, {"%": 0}, {"%": 1221}], "O": [{"%": 1222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}, {"%": 1223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1222}, {"%": 21}], "O": [{"%": 1224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 1224}, {"%": 20}], "O": [{"%": 1225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 1225}, {"%": 0}, {"%": 1226}], "O": [{"%": 1227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/"}, "N": "struct_name"}], "I": [{"%": 1214}, {"%": 1227}], "O": [{"%": 1229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999974752427e-07}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 1229}, {"%": 19}, {"%": 18}], "O": [{"%": 1230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 1231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 1232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 120.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1233}, {"%": 1234}, {"%": 1127}, {"%": 1235}], "O": [{"%": 1236, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.stack", "A": [{"AT": {"#": "0.a_i32", "D": 0}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1236}], "O": [{"%": 1237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1230}, {"%": 1237}], "O": [{"%": 1238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1238}], "O": [{"%": 1239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1239}, {"%": 17}], "O": [{"%": 1240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1240}, {"%": 14}, {"%": 13}, {"%": 16}, {"%": 15}], "O": [{"%": 1241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}}, {"%": 1242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/Swish/"}, "N": "struct_name"}], "I": [{"%": 1241}], "O": [{"%": 1247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1107}, {"%": 1247}], "O": [{"%": 1249, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 1249}, {"%": 1248}], "O": [{"%": 1250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 4096, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1250}, {"%": 12}], "O": [{"%": 1251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1251}, {"%": 9}, {"%": 8}, {"%": 11}, {"%": 10}], "O": [{"%": 1252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, -1], "NCHW", [], 0]}}, {"%": 1253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 1257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/Swish/"}, "N": "struct_name"}], "I": [{"%": 1252}], "O": [{"%": 1258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1258}, {"%": 7}], "O": [{"%": 1259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1259}, {"%": 4}, {"%": 3}, {"%": 6}, {"%": 5}], "O": [{"%": 1260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}, {"%": 1261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 1265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/Swish/"}, "N": "struct_name"}], "I": [{"%": 1260}], "O": [{"%": 1266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.squeeze", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [{"%": 1266}, {"%": 1267}], "O": [{"%": 1268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [{"%": 1268}], "O": [{"%": 1269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/Linear/"}, "N": "struct_name"}], "I": [{"%": 1269}, {"%": 2}], "O": [{"%": 1270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 254], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/Linear/"}, "N": "struct_name"}], "I": [{"%": 1270}, {"%": 1}], "O": [{"%": 1271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 254], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": 2}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/"}, "N": "struct_name"}], "I": [{"%": 1271}], "O": [{"%": 1272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 254], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.fetch", "A": [{"AT": {"#": "0.a_str", "D": "fetch_name_0"}, "N": "name"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "col"}], "I": [{"%": 1272}], "O": [{"%": 1273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 254], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "persistable"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}]}]}]}}