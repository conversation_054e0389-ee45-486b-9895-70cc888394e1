#!/usr/bin/env python3
"""
主窗口模块 - 图纸标注系统的主界面（重构版本）
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QDialog, 
    QApplication, QMessageBox
)
from PySide6.QtCore import Qt, QObject, Signal, QThreadPool, QTimer, QPointF, QRectF, QEvent
from PySide6.QtGui import QPixmap, QColor, QKeySequence

# 导入重构后的模块
from ui.ui_setup import UISetup
from ui.ocr_manager import OCRManager
from ui.file_manager import FileManager
from ui.annotation_manager import AnnotationManager
from utils.constants import APP_TITLE, WINDOW_MIN_SIZE, WINDOW_DEFAULT_SIZE
from utils.dependencies import HAS_PADDLE_OCR, HAS_OCR_SUPPORT

# 获取已配置的logger
logger = logging.getLogger('PyQtBubble.MainWindow')


class LoadingDialog(QDialog):
    """加载对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("加载中...")
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint)
        
        layout = QVBoxLayout(self)
        self.label = QLabel("正在加载...")
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)
        
        self.setFixedSize(300, 100)


class MainWindow(QMainWindow):
    """主窗口类 - 重构版本，整合各个功能模块"""
    
    def __init__(self):
        super().__init__()
        
        # 创建线程池
        self.thread_pool = QThreadPool()
        
        # 初始化基本属性
        self.current_pixmap = None
        self.current_file_path = ""
        
        # 创建加载对话框
        self.loading_dialog = LoadingDialog(self)
        self.loading_label = self.loading_dialog.label
        
        # 初始化各个管理器
        self.setup_managers()
        
        # 设置UI
        self.ui_setup = UISetup(self)
        self.ui_setup.setup_ui()
        
        # 设置管理器的UI组件
        self.setup_manager_ui_components()
        
        # 连接管理器之间的信号
        self.connect_manager_signals()
        
        logger.debug("MainWindow初始化完成")
        
    def setup_managers(self):
        """初始化各个功能管理器"""
        # 创建各个管理器
        self.ocr_manager = OCRManager(self)
        self.file_manager = FileManager(self)
        self.annotation_manager = AnnotationManager(self)
        
    def setup_manager_ui_components(self):
        """为各个管理器设置UI组件"""
        # 设置OCR管理器的UI组件
        if hasattr(self, 'ocr_manager'):
            self.ocr_manager.setup_connections()
            
        # 设置文件管理器的PDF导航控件
        if hasattr(self, 'file_manager') and hasattr(self, 'status_bar'):
            self.file_manager.setup_pdf_navigation_controls(self.status_bar)
            
    def connect_manager_signals(self):
        """连接各个管理器之间的信号"""
        # 文件管理器信号连接
        if hasattr(self, 'file_manager'):
            self.file_manager.file_loaded.connect(self.on_file_loaded)
            self.file_manager.pdf_file_loaded.connect(self.on_pdf_file_loaded)
            self.file_manager.file_load_error.connect(self.on_file_load_error)
            self.file_manager.pdf_page_loaded.connect(self.on_pdf_page_loaded)
            
        # OCR管理器信号连接
        if hasattr(self, 'ocr_manager'):
            self.ocr_manager.ocr_finished.connect(self.on_ocr_finished)
            self.ocr_manager.ocr_error.connect(self.on_ocr_error)
            self.ocr_manager.area_ocr_finished.connect(self.on_area_ocr_finished)
            
        # 标注管理器信号连接
        if hasattr(self, 'annotation_manager'):
            self.annotation_manager.annotation_created.connect(self.on_annotation_created)
            self.annotation_manager.annotation_selected.connect(self.on_annotation_selected)
            self.annotation_manager.annotation_deleted.connect(self.on_annotation_deleted)
            self.annotation_manager.current_annotation_changed.connect(self.on_current_annotation_changed)
            
    def on_file_loaded(self, file_path: str, pixmap: QPixmap):
        """文件加载完成处理"""
        self.current_file_path = file_path
        self.current_pixmap = pixmap
        
        # 清除场景并添加图像
        if hasattr(self, 'graphics_scene'):
            self.graphics_scene.clear()
            self.graphics_scene.addPixmap(pixmap)
            
        # 居中显示
        self.center_view()
        
        # 更新状态栏
        file_name = Path(file_path).name
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage(
                f"已加载: {file_name} ({pixmap.width()}x{pixmap.height()})", 5000
            )
            
        # 隐藏加载对话框
        self.loading_dialog.hide()
        
    def on_pdf_file_loaded(self, file_path: str, page_count: int):
        """PDF文件加载完成处理"""
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage(
                f"PDF文件加载完成，共 {page_count} 页", 3000
            )
            
    def on_file_load_error(self, error_msg: str):
        """文件加载错误处理"""
        QMessageBox.warning(self, "加载错误", error_msg)
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage(f"❌ 文件加载失败: {error_msg}", 5000)
        self.loading_dialog.hide()
        
    def on_pdf_page_loaded(self, pixmap: QPixmap, temp_path: str):
        """PDF页面加载完成处理"""
        self.current_pixmap = pixmap
        
        # 更新场景
        if hasattr(self, 'graphics_scene'):
            self.graphics_scene.clear()
            self.graphics_scene.addPixmap(pixmap)
            
        # 恢复页面数据
        self.restore_page_data(self.file_manager.current_pdf_page)
        
        # 居中显示
        self.center_view()
        
        # 隐藏加载对话框
        self.loading_dialog.hide()
        
    def on_ocr_finished(self, results: List[dict], existing_results: List[dict]):
        """OCR识别完成处理"""
        if hasattr(self, 'status_bar'):
            total_count = len(results) + len(existing_results)
            self.status_bar.showMessage(f"OCR识别完成，共识别 {total_count} 个文本", 3000)
            
    def on_ocr_error(self, error_msg: str):
        """OCR识别错误处理"""
        QMessageBox.critical(self, "OCR识别错误", error_msg)
        
    def on_area_ocr_finished(self, results: List[dict], area: QRectF, 
                           lang_code: str, confidence: int, 
                           cpu_threads: int, force_cpu: bool):
        """区域OCR识别完成处理"""
        if results:
            # 创建标注
            for result in results:
                annotation = self.annotation_manager.create_annotation_from_ocr_result(result)
                if annotation:
                    logger.debug(f"从区域OCR创建标注: {annotation.annotation_id}")
                    
    def on_annotation_created(self, annotation):
        """标注创建完成处理"""
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage(f"创建标注 #{annotation.annotation_id}", 2000)
            
    def on_annotation_selected(self, annotation):
        """标注选择处理"""
        # 更新属性编辑器
        if hasattr(self, 'property_editor'):
            self.property_editor.set_annotation(annotation, self.annotation_manager, self.ocr_manager)
            
    def on_annotation_deleted(self, annotation):
        """标注删除处理"""
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage(f"删除标注 #{annotation.annotation_id}", 2000)
            
    def on_current_annotation_changed(self, annotation):
        """当前标注变化处理"""
        # 更新属性编辑器
        if hasattr(self, 'property_editor'):
            self.property_editor.set_annotation(annotation, self.annotation_manager, self.ocr_manager)
            
    def center_view(self):
        """居中显示图像"""
        if hasattr(self, 'graphics_view') and self.current_pixmap:
            self.graphics_view.fit_in_view()
            
    def save_current_page_data(self):
        """保存当前页面的数据（用于多页PDF）"""
        if not hasattr(self.file_manager, 'pdf_file_path') or not self.file_manager.pdf_file_path:
            return
            
        current_page = self.file_manager.current_pdf_page
        
        # 保存标注数据
        annotations_data = []
        for annotation in self.annotation_manager.annotations:
            ann_data = {
                'annotation_id': annotation.annotation_id,
                'pos_x': annotation.pos().x(),
                'pos_y': annotation.pos().y(),
                'text': annotation.text,
                'dimension': annotation.dimension,
                'dimension_type': annotation.dimension_type,
                'upper_tolerance': annotation.upper_tolerance,
                'lower_tolerance': annotation.lower_tolerance,
                'is_audited': annotation.is_audited,
                'style': annotation.style,
                'shape': annotation.shape,
                'custom_color': annotation.custom_color.name() if annotation.custom_color else None,
                'radius': annotation.radius,
                'scale_factor': annotation.scale_factor
            }
            annotations_data.append(ann_data)
            
        self.file_manager.annotations_by_page[current_page] = annotations_data
        
        # 保存OCR结果
        self.file_manager.ocr_results_by_page[current_page] = self.ocr_manager.ocr_results.copy()
        
    def restore_page_data(self, page_index: int):
        """恢复指定页面的数据"""
        if not hasattr(self.file_manager, 'annotations_by_page'):
            return
            
        # 清除当前标注
        self.annotation_manager.clear_annotations(show_empty_message=False)
        
        # 恢复标注数据
        page_annotations = self.file_manager.annotations_by_page.get(page_index, [])
        for ann_data in page_annotations:
            # 重新创建标注对象
            annotation = self.annotation_manager._create_new_annotation(
                anchor_point=QPointF(ann_data['pos_x'], ann_data['pos_y']),
                text=ann_data['text'],
                dimension=ann_data['dimension'],
                dimension_type=ann_data['dimension_type'],
                style=ann_data.get('style', 'default')
            )
            
            # 恢复其他属性
            annotation.annotation_id = ann_data['annotation_id']
            annotation.set_upper_tolerance(ann_data.get('upper_tolerance', ''))
            annotation.set_lower_tolerance(ann_data.get('lower_tolerance', ''))
            annotation.set_audited(ann_data.get('is_audited', False))
            
            if ann_data.get('custom_color'):
                annotation.custom_color = QColor(ann_data['custom_color'])
            if ann_data.get('radius'):
                annotation.radius = ann_data['radius']
            if ann_data.get('scale_factor'):
                annotation.scale_factor = ann_data['scale_factor']
                
        # 恢复OCR结果
        page_ocr_results = self.file_manager.ocr_results_by_page.get(page_index, [])
        self.ocr_manager.ocr_results = page_ocr_results.copy()
        self.ocr_manager.display_ocr_results()
        self.ocr_manager.update_ocr_stats()
        
        # 更新标注计数器
        if page_annotations:
            max_id = max(ann_data['annotation_id'] for ann_data in page_annotations)
            self.annotation_manager.annotation_counter = max(self.annotation_manager.annotation_counter, max_id)
            
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # PDF页面导航
        if hasattr(self.file_manager, 'pdf_file_path') and self.file_manager.pdf_file_path:
            if event.key() == Qt.Key_Left:
                self.file_manager.go_to_prev_page()
                return
            elif event.key() == Qt.Key_Right:
                self.file_manager.go_to_next_page()
                return
                
        super().keyPressEvent(event)
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 保存当前页面数据
        if hasattr(self, 'file_manager'):
            self.save_current_page_data()
            
        # 清理临时文件
        if hasattr(self.file_manager, 'pdf_pages_cache'):
            for temp_path in self.file_manager.pdf_pages_cache.values():
                if temp_path and os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
                        
        event.accept()
