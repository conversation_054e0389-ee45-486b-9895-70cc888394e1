#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import cv2
import numpy as np
import json
import sys
import argparse
from paddleocr import PaddleOCR
from typing import List, Tuple, Dict, Any
import time

class ImprovedLargeImageOCR:
    """改进版大尺寸图像OCR处理器"""
    
    def __init__(self, 
                 slice_size: int = 800,
                 overlap_ratio: float = 0.30,
                 confidence_threshold: float = 0.6,
                 nms_threshold: float = 0.5):
        """
        初始化处理器
        
        Args:
            slice_size: 切片大小（正方形）
            overlap_ratio: 重叠比例
            confidence_threshold: 置信度阈值
            nms_threshold: NMS IoU阈值
        """
        self.slice_size = slice_size
        self.overlap_ratio = overlap_ratio
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        
        print(f"🚀 初始化OCR [切片:{slice_size}x{slice_size}, 重叠:{overlap_ratio:.1%}, 置信度:{confidence_threshold}, NMS:{nms_threshold}]")

        det_model_dir = "models/detection"
        rec_model_dir = "models/recognition"

        if not os.path.exists(det_model_dir):
            raise FileNotFoundError(f"检测模型目录不存在: {det_model_dir}")
        if not os.path.exists(rec_model_dir):
            raise FileNotFoundError(f"识别模型目录不存在: {rec_model_dir}")

        required_files = ['inference.pdiparams', 'inference.json', 'inference.yml']
        for file_name in required_files:
            if not os.path.exists(os.path.join(rec_model_dir, file_name)):
                raise FileNotFoundError(f"识别模型文件不存在: {file_name}")

        self.ocr = PaddleOCR(
            text_detection_model_dir=det_model_dir,
            text_recognition_model_dir=rec_model_dir,
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False
        )
        print("✅ OCR初始化完成")

    def distance(self, point1, point2):
        """计算两点间距离"""
        point1 = np.array(point1)
        point2 = np.array(point2)
        return np.linalg.norm(point2 - point1)

    def get_bbox_info(self, box):
        """分析文本框几何信息和方向 - 来自1.py的经过验证的逻辑"""
        info_dict = {}
        if len(box) > 3:
            distances = [
                self.distance(box[0], box[1]),
                self.distance(box[1], box[2]),
                self.distance(box[2], box[3]),
                self.distance(box[3], box[0])
            ]

            short_side = int(min(distances))
            long_side = int(max(distances))

            sorted_by_y = sorted(box, key=lambda x: x[1], reverse=True)
            sorted_lst_down = sorted(sorted_by_y[:2], key=lambda m:m[0])
            sorted_lst_top = sorted(sorted_by_y[2:], key=lambda m:m[0])

            left_down = sorted_lst_down[0]
            right_down = sorted_lst_down[1]
            left_top = sorted_lst_top[0]
            right_top = sorted_lst_top[1]

            ori_pt = [left_top, right_top, right_down, left_down]

            down_diff = right_down[0] - left_down[0]

            if abs(down_diff - short_side) < 2:
                img_vertical = True
            else:
                img_vertical = False

            info_dict = {
                'bbox_long': long_side,
                'bbox_short':short_side,
                'ori_pt':ori_pt,
                'is_vertical':img_vertical
            }
        return info_dict

    def rectify_crop(self, img, info):
        """透视校正和裁剪文本区域 - 来自1.py的经过验证的逻辑"""
        try:
            h, w = img.shape[:2]
            left_top, right_top, right_down, left_down = info['ori_pt']
            if info['is_vertical']:
                crop_h = info['bbox_long']
                crop_w = info['bbox_short']
            else:
                crop_h = info['bbox_short']
                crop_w = info['bbox_long']

            new_ld = left_down
            new_lt = [new_ld[0], new_ld[1] - crop_h]
            new_rt = [new_ld[0] + crop_w, new_ld[1] - crop_h]
            new_rd = [new_ld[0] + crop_w, new_ld[1]]

            pts1 = np.array([left_top, right_top, right_down, left_down], dtype=np.float32)
            pts2 = np.array([new_lt, new_rt, new_rd, new_ld], dtype=np.float32)

            M = cv2.getPerspectiveTransform(pts1, pts2)
            dst = cv2.warpPerspective(img, M, (w, h))

            new_crop = dst[max(new_lt[1] - 10, 0):min(new_ld[1] + 10, h), max(new_lt[0] - 10, 0):min(new_rt[0] + 10, w)]

            if info['is_vertical']:
                new_crop = cv2.rotate(new_crop, cv2.ROTATE_90_CLOCKWISE)

            return new_crop

        except Exception as e:
            print(f"⚠️ 透视校正失败: {e}")
            # 降级处理：直接使用边界框裁剪
            x_coords = [point[0] for point in info['ori_pt']]
            y_coords = [point[1] for point in info['ori_pt']]
            x1, y1 = max(0, int(min(x_coords))), max(0, int(min(y_coords)))
            x2, y2 = min(w, int(max(x_coords))), min(h, int(max(y_coords)))
            return img[y1:y2, x1:x2] if x2 > x1 and y2 > y1 else None











    def get_slice_coordinates(self, image_height: int, image_width: int) -> List[Tuple[int, int, int, int]]:
        """计算切片坐标"""
        overlap_pixels = int(self.slice_size * self.overlap_ratio)
        step_size = self.slice_size - overlap_pixels
        coordinates = []

        y = 0
        while y < image_height:
            x = 0
            while x < image_width:
                x2 = min(x + self.slice_size, image_width)
                y2 = min(y + self.slice_size, image_height)
                coordinates.append((x, y, x2, y2))

                if x2 >= image_width:
                    break
                x += step_size

            if y2 >= image_height:
                break
            y += step_size

        return coordinates
    
    def process_single_slice(self, image: np.ndarray, slice_coord: Tuple[int, int, int, int],
                           slice_index: int = 0, save_slices: bool = False, output_dir: str = "./",
                           image_name: str = "unknown") -> List[Dict]:
        """处理单个切片 - 集成1.py的透视校正和旋转逻辑"""
        x1, y1, x2, y2 = slice_coord
        slice_img = image[y1:y2, x1:x2]

        if save_slices:
            slices_dir = os.path.join(output_dir, "slices", f"{image_name}_slices")
            os.makedirs(slices_dir, exist_ok=True)
            slice_filename = f"{image_name}_slice_{slice_index:03d}_x{x1}-{x2}_y{y1}-{y2}.jpg"
            cv2.imwrite(os.path.join(slices_dir, slice_filename), slice_img)

        try:
            # 第一步：检测文本框
            results = self.ocr.predict(slice_img)
            slice_results = []

            if results:
                for result in results:
                    result_data = result.json if hasattr(result, 'json') else result
                    if isinstance(result_data, dict) and 'res' in result_data:
                        ocr_res = result_data['res']
                        dt_polys = ocr_res.get('dt_polys', [])
                        rec_texts = ocr_res.get('rec_texts', [])
                        rec_scores = ocr_res.get('rec_scores', [])

                        # 第二步：对每个检测到的文本框进行透视校正和重新识别
                        crop_img_list = []
                        valid_polys = []

                        for i, poly in enumerate(dt_polys):
                            if poly is not None:
                                original_confidence = rec_scores[i] if i < len(rec_scores) else 0.0

                                # 过滤低置信度结果
                                if original_confidence >= self.confidence_threshold:
                                    # 分析文本框几何信息
                                    bbox_info = self.get_bbox_info(poly)
                                    if bbox_info:
                                        # 透视校正和裁剪
                                        crop_img = self.rectify_crop(slice_img, bbox_info)
                                        if crop_img is not None and crop_img.size > 0:
                                            crop_img_list.append(crop_img)
                                            valid_polys.append((poly, bbox_info))

                        # 第三步：对校正后的图像进行重新识别
                        if crop_img_list:
                            try:
                                # 使用识别模型对校正后的图像进行识别
                                rectified_results = []
                                for crop_img in crop_img_list:
                                    crop_result = self.ocr.predict(crop_img)
                                    if crop_result:
                                        for crop_res in crop_result:
                                            crop_data = crop_res.json if hasattr(crop_res, 'json') else crop_res
                                            if isinstance(crop_data, dict) and 'res' in crop_data:
                                                crop_ocr_res = crop_data['res']
                                                crop_rec_texts = crop_ocr_res.get('rec_texts', [])
                                                crop_rec_scores = crop_ocr_res.get('rec_scores', [])

                                                if crop_rec_texts and crop_rec_scores:
                                                    rectified_results.append({
                                                        'text': crop_rec_texts[0],
                                                        'score': crop_rec_scores[0]
                                                    })
                                                else:
                                                    rectified_results.append({'text': '', 'score': 0.0})
                                            else:
                                                rectified_results.append({'text': '', 'score': 0.0})
                                    else:
                                        rectified_results.append({'text': '', 'score': 0.0})

                                # 第四步：整理最终结果
                                for i, (poly, bbox_info) in enumerate(valid_polys):
                                    if i < len(rectified_results):
                                        final_text = rectified_results[i]['text']
                                        final_score = rectified_results[i]['score']

                                        # 转换坐标到全局坐标系
                                        global_poly = []
                                        for point in poly:
                                            global_x = int(point[0]) + x1
                                            global_y = int(point[1]) + y1
                                            global_poly.append([global_x, global_y])

                                        # 计算边界框（用于NMS）
                                        x_coords = [point[0] for point in global_poly]
                                        y_coords = [point[1] for point in global_poly]
                                        bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]

                                        slice_results.append({
                                            'bbox': bbox,  # [x1, y1, x2, y2] 格式
                                            'poly': global_poly,  # 原始多边形
                                            'text': final_text,  # 透视校正后的识别文本
                                            'score': final_score,  # 透视校正后的置信度
                                            'slice_coord': slice_coord,
                                            'bbox_info': bbox_info,  # 几何信息
                                            'is_vertical': bbox_info.get('is_vertical', False)  # 是否为竖直文本
                                        })

                            except Exception as e:
                                print(f"⚠️ 透视校正后识别失败: {e}")
                                # 降级处理：使用原始识别结果
                                for i, (poly, bbox_info) in enumerate(valid_polys):
                                    original_text = rec_texts[i] if i < len(rec_texts) else ""
                                    original_confidence = rec_scores[i] if i < len(rec_scores) else 0.0

                                    global_poly = []
                                    for point in poly:
                                        global_x = int(point[0]) + x1
                                        global_y = int(point[1]) + y1
                                        global_poly.append([global_x, global_y])

                                    x_coords = [point[0] for point in global_poly]
                                    y_coords = [point[1] for point in global_poly]
                                    bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]

                                    slice_results.append({
                                        'bbox': bbox,
                                        'poly': global_poly,
                                        'text': original_text,
                                        'score': original_confidence,
                                        'slice_coord': slice_coord,
                                        'bbox_info': bbox_info,
                                        'is_vertical': bbox_info.get('is_vertical', False)
                                    })

            return slice_results

        except Exception as e:
            print(f"⚠️ 处理切片 {slice_coord} 时出错: {e}")
            return []
    
    def apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """应用NMS去重算法"""
        if not detections:
            return []

        detections = sorted(detections, key=lambda x: x['score'], reverse=True)
        keep = []

        for current in detections:
            is_suppressed = False

            for kept in keep:
                iou = self._calculate_bbox_iou(current['bbox'], kept['bbox'])
                if iou > self.nms_threshold:
                    is_suppressed = True
                    break

            if not is_suppressed:
                keep.append(current)

        return keep

    def merge_cross_slice_texts(self, detections: List[Dict]) -> List[Dict]:
        """
        合并跨切片分割的文本框

        SAHI切片分割问题分析：
        1. 固定尺寸切片无法适应文本的自然边界
        2. 长文本（如"100mm x 50mm"）可能跨越多个切片
        3. 重叠区域可能导致同一文本的不同部分被重复检测
        4. 文本检测模型在切片边界处容易产生不完整的检测框
        """
        if len(detections) < 2:
            return detections

        print(f"🔗 开始跨切片文本合并，输入 {len(detections)} 个文本框")

        # 按y坐标排序，便于处理水平排列的文本
        detections = sorted(detections, key=lambda x: (x['bbox'][1], x['bbox'][0]))

        merged_detections = []
        used_indices = set()

        for i, current in enumerate(detections):
            if i in used_indices:
                continue

            # 寻找可能的合并候选
            merge_candidates = [current]
            merge_indices = [i]

            for j, candidate in enumerate(detections[i+1:], i+1):
                if j in used_indices:
                    continue

                if self._should_merge_texts(current, candidate):
                    merge_candidates.append(candidate)
                    merge_indices.append(j)

            # 如果找到合并候选，进行合并
            if len(merge_candidates) > 1:
                merged_text = self._merge_text_boxes(merge_candidates)
                merged_detections.append(merged_text)
                used_indices.update(merge_indices)
                print(f"  ✅ 合并 {len(merge_candidates)} 个文本框: '{merged_text['text']}'")
            else:
                merged_detections.append(current)
                used_indices.add(i)

        print(f"🔗 合并完成，输出 {len(merged_detections)} 个文本框")
        return merged_detections

    def _should_merge_texts(self, text1: Dict, text2: Dict) -> bool:
        """判断两个文本框是否应该合并"""
        bbox1, bbox2 = text1['bbox'], text2['bbox']
        text1_content, text2_content = text1.get('text', ''), text2.get('text', '')

        # 1. 空间位置关系判断
        if not self._is_spatially_adjacent(bbox1, bbox2):
            return False

        # 2. 文本内容语义分析
        if not self._is_semantically_related(text1_content, text2_content):
            return False

        # 3. 切片边界检查
        if not self._is_cross_slice_candidate(text1, text2):
            return False

        return True

    def _is_spatially_adjacent(self, bbox1: List[float], bbox2: List[float]) -> bool:
        """检查两个文本框是否在空间上相邻"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # 计算文本框的中心点和尺寸
        center1_x, center1_y = (x1_1 + x2_1) / 2, (y1_1 + y2_1) / 2
        center2_x, center2_y = (x1_2 + x2_2) / 2, (y1_2 + y2_2) / 2

        height1, width1 = y2_1 - y1_1, x2_1 - x1_1
        height2, width2 = y2_2 - y1_2, x2_2 - x1_2

        # 垂直对齐检查（y坐标相近）
        vertical_alignment = abs(center1_y - center2_y) < max(height1, height2) * 0.5

        # 水平距离检查
        horizontal_distance = abs(center1_x - center2_x)
        max_expected_distance = (width1 + width2) * 0.8  # 允许一定的间隔

        # 水平相邻检查
        horizontal_adjacent = (
            (x2_1 >= x1_2 - max(width1, width2) * 0.3) and
            (x1_1 <= x2_2 + max(width1, width2) * 0.3)
        )

        return vertical_alignment and horizontal_adjacent

    def _is_semantically_related(self, text1: str, text2: str) -> bool:
        """检查两个文本是否在语义上相关"""
        import re

        # 尺寸标注模式
        dimension_patterns = [
            r'\d+\.?\d*\s*(mm|cm|m|inch|in|")',  # 数字+单位
            r'\d+\.?\d*\s*[xX×]\s*\d+\.?\d*',    # 尺寸格式 (如 100x50)
            r'[±∅φΦ]\s*\d+\.?\d*',               # 公差和直径符号
            r'\d+\.?\d*\s*[°℃℉%]',               # 角度、温度、百分比
        ]

        # 技术规格模式
        spec_patterns = [
            r'[A-Z]\d+',                         # 规格代码 (如 M8, A4)
            r'\d+\.?\d*\s*[VAWΩ]',               # 电气单位
            r'\d+\.?\d*\s*(Hz|kHz|MHz|GHz)',     # 频率单位
        ]

        all_patterns = dimension_patterns + spec_patterns

        # 检查是否包含相关模式
        text1_has_pattern = any(re.search(pattern, text1, re.IGNORECASE) for pattern in all_patterns)
        text2_has_pattern = any(re.search(pattern, text2, re.IGNORECASE) for pattern in all_patterns)

        # 如果两个文本都包含技术模式，很可能是相关的
        if text1_has_pattern and text2_has_pattern:
            return True

        # 检查数字和单位的组合
        has_number1 = bool(re.search(r'\d+\.?\d*', text1))
        has_number2 = bool(re.search(r'\d+\.?\d*', text2))
        has_unit1 = bool(re.search(r'(mm|cm|m|inch|in|"|°|℃|℉|%|V|A|W|Ω|Hz)', text1, re.IGNORECASE))
        has_unit2 = bool(re.search(r'(mm|cm|m|inch|in|"|°|℃|℉|%|V|A|W|Ω|Hz)', text2, re.IGNORECASE))

        # 一个有数字，另一个有单位
        if (has_number1 and has_unit2) or (has_unit1 and has_number2):
            return True

        # 检查连接符号
        connectors = ['x', 'X', '×', '-', '±', '∅', 'φ', 'Φ']
        if any(conn in text1 or conn in text2 for conn in connectors):
            return True

        return False

    def _is_cross_slice_candidate(self, text1: Dict, text2: Dict) -> bool:
        """检查是否为跨切片候选"""
        # 检查是否来自相邻的切片
        slice1 = text1.get('slice_coord')
        slice2 = text2.get('slice_coord')

        if not slice1 or not slice2:
            return True  # 如果没有切片信息，假设可能是跨切片的

        # 检查切片是否相邻
        x1_1, y1_1, x2_1, y2_1 = slice1
        x1_2, y1_2, x2_2, y2_2 = slice2

        # 水平相邻的切片
        horizontal_adjacent = (
            abs(x2_1 - x1_2) < 50 or abs(x2_2 - x1_1) < 50
        ) and abs(y1_1 - y1_2) < 50

        # 垂直相邻的切片
        vertical_adjacent = (
            abs(y2_1 - y1_2) < 50 or abs(y2_2 - y1_1) < 50
        ) and abs(x1_1 - x1_2) < 50

        return horizontal_adjacent or vertical_adjacent

    def _merge_text_boxes(self, text_boxes: List[Dict]) -> Dict:
        """合并多个文本框"""
        if len(text_boxes) == 1:
            return text_boxes[0]

        # 按x坐标排序，确保文本顺序正确
        text_boxes = sorted(text_boxes, key=lambda x: x['bbox'][0])

        # 合并边界框
        all_x1 = [box['bbox'][0] for box in text_boxes]
        all_y1 = [box['bbox'][1] for box in text_boxes]
        all_x2 = [box['bbox'][2] for box in text_boxes]
        all_y2 = [box['bbox'][3] for box in text_boxes]

        merged_bbox = [min(all_x1), min(all_y1), max(all_x2), max(all_y2)]

        # 合并文本内容
        texts = [box.get('text', '') for box in text_boxes]
        merged_text = self._smart_text_concatenation(texts)

        # 计算加权置信度
        confidences = [box.get('score', 0.0) for box in text_boxes]
        bbox_areas = [(box['bbox'][2] - box['bbox'][0]) * (box['bbox'][3] - box['bbox'][1]) for box in text_boxes]
        total_area = sum(bbox_areas)

        if total_area > 0:
            weighted_confidence = sum(conf * area / total_area for conf, area in zip(confidences, bbox_areas))
        else:
            weighted_confidence = sum(confidences) / len(confidences)

        # 合并多边形（取第一个的多边形，或重新计算）
        merged_poly = text_boxes[0].get('poly', [
            [merged_bbox[0], merged_bbox[1]],
            [merged_bbox[2], merged_bbox[1]],
            [merged_bbox[2], merged_bbox[3]],
            [merged_bbox[0], merged_bbox[3]]
        ])

        return {
            'bbox': merged_bbox,
            'poly': merged_poly,
            'text': merged_text,
            'score': weighted_confidence,
            'slice_coord': text_boxes[0].get('slice_coord'),
            'bbox_info': text_boxes[0].get('bbox_info'),
            'merged_from': len(text_boxes),  # 标记这是合并的结果
            'original_texts': texts  # 保留原始文本用于调试
        }

    def _smart_text_concatenation(self, texts: List[str]) -> str:
        """智能文本连接"""
        import re

        # 清理空文本
        texts = [t.strip() for t in texts if t.strip()]
        if not texts:
            return ""

        if len(texts) == 1:
            return texts[0]

        # 检查是否需要添加连接符
        result = texts[0]

        for i in range(1, len(texts)):
            current_text = texts[i]

            # 检查前一个文本的结尾和当前文本的开头
            prev_end = result[-1] if result else ''
            curr_start = current_text[0] if current_text else ''

            # 如果是数字和单位的组合，添加空格
            if (re.match(r'\d', prev_end) and re.match(r'[a-zA-Z]', curr_start)) or \
               (re.match(r'[a-zA-Z]', prev_end) and re.match(r'\d', curr_start)):
                result += ' ' + current_text
            # 如果是符号连接，直接连接
            elif prev_end in 'xX×-±∅φΦ' or curr_start in 'xX×-±∅φΦ':
                result += current_text
            # 默认添加空格
            else:
                result += ' ' + current_text

        return result.strip()

    def filter_contained_boxes(self, detections: List[Dict]) -> List[Dict]:
        """
        过滤被包含的文本框

        解决SAHI切片处理中的文本框嵌套问题：
        1. 切片边界处理可能产生重叠检测
        2. 重叠区域可能同时检测到完整文本和部分文本
        3. 不同尺度的检测可能产生大小文本框的包含关系
        """
        if len(detections) < 2:
            return detections

        print(f"🔍 开始包含关系过滤，输入 {len(detections)} 个文本框")

        # 按面积排序，大文本框在前
        detections = sorted(detections, key=lambda x: self._calculate_bbox_area(x['bbox']), reverse=True)

        filtered_detections = []
        removed_count = 0

        for i, current in enumerate(detections):
            is_contained = False

            # 检查当前文本框是否被之前的（更大的）文本框包含
            for j in range(i):
                container = detections[j]

                if self._is_box_contained(current['bbox'], container['bbox']):
                    # 检查置信度，如果被包含的文本框置信度明显更高，则保留它
                    if current['score'] > container['score'] * 1.2:  # 20%的置信度优势
                        continue

                    is_contained = True
                    removed_count += 1
                    print(f"  🗑️ 移除被包含文本框: '{current.get('text', '')}' (被 '{container.get('text', '')}' 包含)")
                    break

            if not is_contained:
                filtered_detections.append(current)

        print(f"🔍 包含关系过滤完成，移除 {removed_count} 个被包含文本框，剩余 {len(filtered_detections)} 个")
        return filtered_detections

    def _calculate_bbox_area(self, bbox: List[float]) -> float:
        """计算边界框面积"""
        x1, y1, x2, y2 = bbox
        return (x2 - x1) * (y2 - y1)

    def _is_box_contained(self, small_bbox: List[float], large_bbox: List[float],
                         containment_threshold: float = 0.8) -> bool:
        """
        检查小文本框是否被大文本框包含

        Args:
            small_bbox: 小文本框 [x1, y1, x2, y2]
            large_bbox: 大文本框 [x1, y1, x2, y2]
            containment_threshold: 包含阈值，默认0.8（80%）

        Returns:
            bool: 是否存在包含关系
        """
        sx1, sy1, sx2, sy2 = small_bbox
        lx1, ly1, lx2, ly2 = large_bbox

        # 计算小文本框的面积
        small_area = (sx2 - sx1) * (sy2 - sy1)
        if small_area <= 0:
            return False

        # 计算大文本框的面积
        large_area = (lx2 - lx1) * (ly2 - ly1)
        if large_area <= small_area:  # 大文本框不能比小文本框小
            return False

        # 计算交集区域
        intersection_x1 = max(sx1, lx1)
        intersection_y1 = max(sy1, ly1)
        intersection_x2 = min(sx2, lx2)
        intersection_y2 = min(sy2, ly2)

        # 检查是否有交集
        if intersection_x1 >= intersection_x2 or intersection_y1 >= intersection_y2:
            return False

        # 计算交集面积
        intersection_area = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)

        # 计算包含比例（小文本框被覆盖的比例）
        containment_ratio = intersection_area / small_area

        return containment_ratio >= containment_threshold

    def _calculate_bbox_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """
        计算两个边界框的IoU（Intersection over Union）
        这是NMS算法的核心计算
        
        Args:
            bbox1, bbox2: [x1, y1, x2, y2] 格式的边界框
            
        Returns:
            IoU值 (0-1)
        """
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # 计算交集区域
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        # 检查是否有交集
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        # 计算交集面积
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算各自面积
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        
        # 计算并集面积
        union = area1 + area2 - intersection
        
        # 返回IoU
        return intersection / union if union > 0 else 0.0
    
    def create_comprehensive_visualization(self, image: np.ndarray, slice_coords: List[Tuple[int, int, int, int]],
                                         before_nms: List[Dict], after_nms: List[Dict], output_dir: str):
        """创建可视化图像"""
        
        # 创建三个可视化图像
        vis_slices = image.copy()
        vis_before_nms = image.copy()
        vis_after_nms = image.copy()
        
        # 1. 切片可视化
        for i, (x1, y1, x2, y2) in enumerate(slice_coords):
            cv2.rectangle(vis_slices, (x1, y1), (x2, y2), (0, 255, 255), 2)  # 黄色
            if i < 50:  # 只显示前50个切片的编号，避免过于拥挤
                cv2.putText(vis_slices, f"S{i+1}", (x1+5, y1+25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        cv2.putText(vis_slices, f"Slices: {len(slice_coords)}", (30, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 255), 3)
        
        # 2. NMS前检测结果
        for i, detection in enumerate(before_nms):
            bbox = detection['bbox']
            x1, y1, x2, y2 = [int(coord) for coord in bbox]
            cv2.rectangle(vis_before_nms, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红色
        
        cv2.putText(vis_before_nms, f"Before NMS: {len(before_nms)}", (30, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        
        # 3. NMS后检测结果（包含识别文本）
        for i, detection in enumerate(after_nms):
            bbox = detection['bbox']
            x1, y1, x2, y2 = [int(coord) for coord in bbox]
            cv2.rectangle(vis_after_nms, (x1, y1), (x2, y2), (0, 255, 0), 3)  # 绿色

            # 显示序号
            cv2.putText(vis_after_nms, str(i+1), (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

            # 显示识别的文本（如果有）
            if 'text' in detection and detection['text']:
                text = detection['text'][:10] + "..." if len(detection['text']) > 10 else detection['text']
                cv2.putText(vis_after_nms, text, (x1, y2+20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                # 显示处理信息标记
                marker_x = x2 - 15
                marker_y = y1 + 15

                # 竖直文本标记
                if detection.get('is_vertical', False):
                    cv2.putText(vis_after_nms, "V", (marker_x, marker_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 2)  # 紫色V表示竖直
                    marker_x -= 15

                # 跨切片合并标记
                if detection.get('merged_from', 0) > 1:
                    cv2.putText(vis_after_nms, "M", (marker_x, marker_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 165, 255), 2)  # 橙色M表示合并
        
        cv2.putText(vis_after_nms, f"After NMS: {len(after_nms)}", (30, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 0), 3)
        
        # 保存可视化图像
        images_to_save = [
            (vis_slices, "slices"),
            (vis_before_nms, "before_nms"),
            (vis_after_nms, "after_nms")
        ]
        
        for vis_image, name in images_to_save:
            h, w = vis_image.shape[:2]
            full_path = os.path.join(output_dir, f"{name}_full.jpg")
            cv2.imwrite(full_path, vis_image)

            if max(h, w) > 4000:
                scale = 4000 / max(h, w)
                vis_resized = cv2.resize(vis_image, (int(w * scale), int(h * scale)))
                cv2.imwrite(os.path.join(output_dir, f"{name}_scaled.jpg"), vis_resized)

        print(f"🎨 可视化图像已保存到: {output_dir}")
    
    def process_large_image(self, image_path: str, output_dir: str = "./improved_ocr_output/", save_slices: bool = True) -> Dict:
        """
        处理大尺寸图像的主方法（改进版SAHI逻辑）

        Args:
            image_path: 输入图像路径
            output_dir: 输出目录
            save_slices: 是否保存切片图像
        """
        start_time = time.time()
        
        print(f"\n🔍 处理图像: {image_path}")

        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")

        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        height, width = image.shape[:2]
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        os.makedirs(output_dir, exist_ok=True)

        slice_coords = self.get_slice_coordinates(height, width)
        print(f"📊 图像尺寸: {width}x{height}, 生成 {len(slice_coords)} 个切片")

        all_detections = []
        for i, coord in enumerate(slice_coords):
            slice_results = self.process_single_slice(
                image, coord, slice_index=i+1, save_slices=save_slices,
                output_dir=output_dir, image_name=image_name
            )
            all_detections.extend(slice_results)
            print(f"🔍 切片 {i+1}/{len(slice_coords)}: 检测到 {len(slice_results)} 个文本框")
        
        # 步骤1：跨切片文本合并（在NMS之前进行）
        merged_detections = self.merge_cross_slice_texts(all_detections)

        # 步骤2：NMS去重
        print(f"🔄 NMS去重: {len(merged_detections)} → ", end="")
        nms_detections = self.apply_nms(merged_detections)
        print(f"{len(nms_detections)} 个文本框")

        # 步骤3：包含关系过滤（在NMS之后进行）
        final_detections = self.filter_contained_boxes(nms_detections)

        # 统计处理效果
        vertical_count = sum(1 for d in final_detections if d.get('is_vertical', False))
        merged_count = sum(1 for d in final_detections if d.get('merged_from', 0) > 1)

        print(f"🔄 透视校正: {len(final_detections)}个文本框, {vertical_count}个竖直文本")
        print(f"🔗 跨切片合并: {merged_count}个合并文本, 原始{len(all_detections)}→合并后{len(merged_detections)}")
        print(f"🔍 包含关系过滤: NMS后{len(nms_detections)}→最终{len(final_detections)} (移除{len(nms_detections)-len(final_detections)}个嵌套框)")

        # 显示最终识别结果
        print(f"\n📝 最终识别结果 ({len(final_detections)}个):")
        print("-" * 50)
        for i, detection in enumerate(final_detections):
            text = detection.get('text', '未识别')
            score = detection.get('score', 0.0)

            markers = []
            if detection.get('is_vertical', False):
                markers.append("竖直")
            if detection.get('merged_from', 0) > 1:
                markers.append(f"合并{detection['merged_from']}个")

            marker_str = f" [{','.join(markers)}]" if markers else ""
            print(f"  {i+1:2d}. '{text}' ({score:.3f}){marker_str}")

        # 生成综合可视化
        self.create_comprehensive_visualization(image, slice_coords, all_detections, final_detections, output_dir)
        
        # 处理时间
        processing_time = time.time() - start_time
        
        # 准备结果
        result_summary = {
            'image_path': image_path,
            'image_size': [int(width), int(height)],
            'config': {
                'slice_size': int(self.slice_size),
                'overlap_ratio': float(self.overlap_ratio),
                'confidence_threshold': float(self.confidence_threshold),
                'nms_threshold': float(self.nms_threshold)
            },
            'statistics': {
                'total_slices': len(slice_coords),
                'detections_before_nms': len(all_detections),
                'detections_after_nms': len(final_detections),
                'suppressed_detections': len(all_detections) - len(final_detections),
                'processing_time': float(processing_time)
            },
            'final_results': final_detections
        }
        
        # 保存JSON结果
        json_path = os.path.join(output_dir, "improved_ocr_result.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result_summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 处理完成: {width}x{height}, {len(slice_coords)}切片, {len(final_detections)}文本框, {processing_time:.2f}秒")
        print(f"💾 结果保存: {json_path}")
        print(f"� 输出目录: {output_dir}")
        if save_slices:
            print(f"� 切片图像: {len(slice_coords)} 个切片保存在 {os.path.join(output_dir, 'slices')}")
        
        return result_summary

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="改进版大尺寸图像OCR处理器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python scripts/improved_large_image_ocr.py image.png
  python scripts/improved_large_image_ocr.py /path/to/image.jpg
        """
    )

    # 必需参数：图像路径
    parser.add_argument(
        'image_path',
        help='输入图像文件路径'
    )

    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    print("🚀 改进版大尺寸图像OCR处理器")
    print("基于标准SAHI切图与拼接逻辑，集成高效NMS去重算法")
    print("="*70)

    # 固定的默认参数
    output_dir = "./improved_ocr_output/"
    slice_size = 800
    overlap_ratio = 0.30
    confidence_threshold = 0.6
    nms_threshold = 0.5
    save_slices = True

    print("� 改进版大尺寸图像OCR处理器")
    print(f"📋 输入: {args.image_path} | 输出: {output_dir} | 切片: {slice_size}x{slice_size}")

    if not os.path.exists(args.image_path):
        print(f"❌ 文件不存在: {args.image_path}")
        sys.exit(1)

    valid_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif'}
    if os.path.splitext(args.image_path)[1].lower() not in valid_extensions:
        print(f"❌ 不支持的文件格式")
        sys.exit(1)

    try:
        # 创建改进版处理器
        processor = ImprovedLargeImageOCR(
            slice_size=slice_size,
            overlap_ratio=overlap_ratio,
            confidence_threshold=confidence_threshold,
            nms_threshold=nms_threshold
        )

        processor.process_large_image(
            image_path=args.image_path,
            output_dir=output_dir,
            save_slices=save_slices
        )

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断处理")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
