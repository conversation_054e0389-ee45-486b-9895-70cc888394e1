#!/usr/bin/env python3
"""
标注管理模块 - 处理标注创建、编辑、删除、排序功能
"""

import re
import logging
import math
from typing import List, Dict, Optional, Tuple
from datetime import datetime

from PySide6.QtWidgets import QMessageBox, QInputDialog
from PySide6.QtCore import Qt, QObject, Signal, QPointF, QRectF
from PySide6.QtGui import QColor

from core.annotation_item import BubbleAnnotationItem
from utils.constants import (
    STYLE_NAME_REVERSE_MAP, STYLE_NAME_MAP, OCR_TYPE_TO_STYLE,
    BUBBLE_REORDER_GRID_SIZE
)

# 获取已配置的logger
logger = logging.getLogger('PyQtBubble.AnnotationManager')

class AnnotationManager(QObject):
    """标注管理器 - 处理所有标注相关功能"""
    
    # 信号定义
    annotation_created = Signal(object)  # 标注创建信号
    annotation_selected = Signal(object)  # 标注选择信号
    annotation_deleted = Signal(object)  # 标注删除信号
    annotation_moved = Signal(object, QPointF)  # 标注移动信号
    annotations_reordered = Signal()  # 标注重排序信号
    current_annotation_changed = Signal(object)  # 当前标注变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        
        # 标注相关属性
        self.annotations = []
        self.current_annotation = None
        self.annotation_counter = 0
        
        # 标注颜色和样式
        self.annotation_color = QColor(255, 0, 0, 200)  # 默认红色，半透明
        self.next_annotation_color = None  # 用于存储下一个标注的颜色
        self.next_annotation_scale = 1.0  # 用于存储下一个标注的比例因子
        self.next_annotation_size = None  # 用于存储下一个标注的大小
        
    def create_annotation_from_ocr_result(self, ocr_result: dict):
        """从OCR结果创建标注"""
        if 'bbox' not in ocr_result or len(ocr_result['bbox']) < 4:
            return None
            
        # 调试输出 - 检查OCR结果
        print(f"🔍 开始处理OCR结果: {ocr_result.get('text', '')}")
        
        # 计算锚点位置（边界框中心）
        bbox = ocr_result['bbox']
        if hasattr(bbox, '__len__') and len(bbox) >= 4:
            # 计算边界框的中心点
            if hasattr(bbox[0], '__len__'):  # 如果是点列表
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                center_x = sum(x_coords) / len(x_coords)
                center_y = sum(y_coords) / len(y_coords)
            else:  # 如果是[x, y, width, height]格式
                center_x = bbox[0] + bbox[2] / 2
                center_y = bbox[1] + bbox[3] / 2
        else:
            center_x = ocr_result.get('center_x', 0)
            center_y = ocr_result.get('center_y', 0)
        
        anchor_point = QPointF(center_x, center_y)
        
        # 解析OCR文本
        text = ocr_result.get('text', '').strip()
        parsed_data = self._parse_annotation_text(text)
        
        # 确定标注样式
        text_type = ocr_result.get('type', 'annotation')
        style = OCR_TYPE_TO_STYLE.get(text_type, 'default')
        
        # 创建标注
        annotation = self._create_new_annotation(
            anchor_point=anchor_point,
            text=text,
            dimension=parsed_data.get('dimension', ''),
            dimension_type=parsed_data.get('dimension_type', ''),
            style=style
        )
        
        # 设置公差信息
        if parsed_data.get('upper_tolerance'):
            annotation.set_upper_tolerance(parsed_data['upper_tolerance'])
        if parsed_data.get('lower_tolerance'):
            annotation.set_lower_tolerance(parsed_data['lower_tolerance'])
        
        # 存储边界框信息用于预览
        if hasattr(bbox[0], '__len__'):  # 点列表格式
            annotation.bbox_points = [(point[0], point[1]) for point in bbox]
        else:  # 矩形格式
            x, y, w, h = bbox
            annotation.bbox_points = [(x, y), (x+w, y), (x+w, y+h), (x, y+h)]
        
        print(f"✅ 标注创建完成: ID={annotation.annotation_id}, 尺寸={annotation.dimension}, 类型={annotation.dimension_type}")
        
        return annotation

    def _parse_annotation_text(self, text: str) -> dict:
        """解析标注文本，提取尺寸、类型和公差信息"""
        result = {
            'dimension': '',
            'dimension_type': '',
            'upper_tolerance': '',
            'lower_tolerance': ''
        }
        
        if not text:
            return result
        
        print(f"🔍 开始解析文本: '{text}'")
        
        # 清理文本
        text = text.strip()
        
        # 处理换行符分隔的情况
        if '\n' in text:
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            print(f"  📝 检测到多行文本: {lines}")
            
            # 第一行通常是主要尺寸
            if lines:
                main_text = lines[0]
                result['dimension'] = main_text
                
                # 分析主要尺寸的类型
                if 'Φ' in main_text or 'ø' in main_text or '∅' in main_text:
                    result['dimension_type'] = '直径'
                elif '×' in main_text or 'x' in main_text or 'X' in main_text:
                    result['dimension_type'] = '长×宽'
                elif 'M' in main_text and any(c.isdigit() for c in main_text):
                    result['dimension_type'] = '螺纹规格'
                elif '°' in main_text:
                    result['dimension_type'] = '角度'
                else:
                    result['dimension_type'] = '尺寸'
                
                # 处理后续行的公差信息
                for line in lines[1:]:
                    if '+' in line and not result['upper_tolerance']:
                        result['upper_tolerance'] = line
                    elif '-' in line and not result['lower_tolerance']:
                        result['lower_tolerance'] = line
                    elif '±' in line:
                        # 正负公差
                        tolerance_match = re.search(r'±\s*([0-9.]+)', line)
                        if tolerance_match:
                            tolerance_value = tolerance_match.group(1)
                            result['upper_tolerance'] = f"+{tolerance_value}"
                            result['lower_tolerance'] = f"-{tolerance_value}"
        else:
            # 单行文本处理
            result['dimension'] = text
            
            # 分析文本类型
            if 'Φ' in text or 'ø' in text or '∅' in text:
                result['dimension_type'] = '直径'
            elif '×' in text or 'x' in text or 'X' in text:
                result['dimension_type'] = '长×宽'
            elif 'M' in text and any(c.isdigit() for c in text):
                result['dimension_type'] = '螺纹规格'
            elif '°' in text:
                result['dimension_type'] = '角度'
            elif 'Ra' in text or 'Rz' in text:
                result['dimension_type'] = '表面粗糙度'
            else:
                result['dimension_type'] = '尺寸'
        
        print(f"✅ 解析完成: {result}")
        return result

    def _create_new_annotation(self, anchor_point: QPointF, text: str = "", 
                              dimension: str = "", dimension_type: str = "", 
                              style: str = "default"):
        """创建新的标注"""
        # 在创建新标注前，先确定全局最大ID
        if hasattr(self.parent, 'pdf_file_path') and self.parent.pdf_file_path and hasattr(self.parent, 'pdf_page_count') and self.parent.pdf_page_count > 1:
            # 多页PDF模式，计算所有页面中的最大ID
            max_id_across_pages = self.annotation_counter
            
            if hasattr(self.parent, 'annotations_by_page'):
                for page_idx, page_annotations in self.parent.annotations_by_page.items():
                    for ann_data in page_annotations:
                        if isinstance(ann_data, dict) and 'annotation_id' in ann_data:
                            max_id_across_pages = max(max_id_across_pages, ann_data['annotation_id'])
                        elif hasattr(ann_data, 'annotation_id'):
                            max_id_across_pages = max(max_id_across_pages, ann_data.annotation_id)
            
            # 检查当前页面的标注
            for ann in self.annotations:
                max_id_across_pages = max(max_id_across_pages, ann.annotation_id)
            
            # 设置新的ID
            new_id = max_id_across_pages + 1
            self.annotation_counter = new_id
        else:
            # 单页模式或第一页
            self.annotation_counter += 1
            new_id = self.annotation_counter
        
        # 创建标注
        annotation = BubbleAnnotationItem(
            annotation_id=new_id,
            anchor_point=anchor_point,
            text=text or f"标注 {new_id}",
            style=style,
            dimension=dimension,
            dimension_type=dimension_type
        )
        
        # 应用下一个标注的颜色和大小设置
        if self.next_annotation_color:
            annotation.custom_color = self.next_annotation_color
            self.next_annotation_color = None
        
        if self.next_annotation_size:
            annotation.radius = self.next_annotation_size
            self.next_annotation_size = None
        elif self.next_annotation_scale != 1.0:
            annotation.scale_factor = self.next_annotation_scale
            self.next_annotation_scale = 1.0
        
        # 连接信号
        self._connect_annotation_signals(annotation)
        
        # 添加到场景和列表
        if hasattr(self.parent, 'graphics_scene'):
            self.parent.graphics_scene.addItem(annotation)
        
        self.annotations.append(annotation)
        
        # 添加到标注表格
        if hasattr(self.parent, 'annotation_table'):
            self.parent.annotation_table.add_annotation(annotation, {})
        
        # 发出信号
        self.annotation_created.emit(annotation)
        
        return annotation

    def _connect_annotation_signals(self, annotation):
        """连接标注对象的所有信号"""
        annotation.selected.connect(self.on_annotation_selected)
        annotation.moved.connect(self.on_annotation_moved)
        annotation.delete_requested.connect(self.delete_annotation)
        annotation.size_change_requested.connect(self.on_annotation_size_changed)
        annotation.shape_change_requested.connect(self.on_annotation_shape_changed)
        annotation.style_change_requested.connect(self.on_annotation_style_changed)
        annotation.color_change_requested.connect(self.on_annotation_color_changed)
        annotation.data_updated.connect(lambda: self.refresh_annotation_list())

    def on_annotation_selected(self, annotation: BubbleAnnotationItem):
        """标注被选中时的处理"""
        # 取消其他标注的高亮
        for ann in self.annotations:
            ann.set_highlighted(False)
        
        # 设置当前标注
        self.current_annotation = annotation
        annotation.set_highlighted(True)
        
        # 发出信号
        self.annotation_selected.emit(annotation)
        self.current_annotation_changed.emit(annotation)

    def on_annotation_moved(self, annotation: BubbleAnnotationItem, position: QPointF):
        """标注移动时的处理"""
        self.annotation_moved.emit(annotation, position)

    def on_annotation_size_changed(self, annotation: BubbleAnnotationItem):
        """标注大小变化时的处理"""
        # 这里可以添加大小变化的处理逻辑
        pass

    def on_annotation_shape_changed(self, annotation: BubbleAnnotationItem):
        """标注形状变化时的处理"""
        if annotation == self.current_annotation:
            self.on_annotation_selected(annotation)

    def on_annotation_style_changed(self, annotation: BubbleAnnotationItem):
        """标注样式变化时的处理"""
        if annotation == self.current_annotation:
            self.on_annotation_selected(annotation)

    def on_annotation_color_changed(self, annotation: BubbleAnnotationItem):
        """标注颜色变化时的处理"""
        if annotation == self.current_annotation:
            self.on_annotation_selected(annotation)

    def select_annotation_by_id(self, annotation_id: int):
        """根据ID选中标注"""
        try:
            # 查找对应的标注
            found_annotation = None
            for annotation in self.annotations:
                if annotation.annotation_id == annotation_id:
                    found_annotation = annotation
                    break
            
            if not found_annotation:
                print(f"未找到ID为 {annotation_id} 的标注")
                return
            
            # 选中标注
            self.on_annotation_selected(found_annotation)
            
        except Exception as e:
            print(f"选中标注时出错: {e}")

    def delete_annotation(self, annotation: BubbleAnnotationItem):
        """删除标注"""
        try:
            annotation_id_to_delete = annotation.annotation_id

            # 查找与此标注关联的OCR边界框
            if hasattr(self.parent, 'graphics_scene'):
                bbox_items_to_remove = []
                for item in self.parent.graphics_scene.items():
                    if hasattr(item, 'is_ocr_bbox') and item.is_ocr_bbox:
                        # 检查是否与此标注关联
                        if hasattr(item, 'associated_annotation_id') and item.associated_annotation_id == annotation_id_to_delete:
                            bbox_items_to_remove.append(item)

                # 移除关联的OCR边界框
                for bbox_item in bbox_items_to_remove:
                    self.parent.graphics_scene.removeItem(bbox_item)

            # 从场景中移除标注
            if hasattr(self.parent, 'graphics_scene'):
                self.parent.graphics_scene.removeItem(annotation)
            
            # 从标注列表中移除
            if annotation in self.annotations:
                self.annotations.remove(annotation)
            
            # 从标注表格中移除
            if hasattr(self.parent, 'annotation_table'):
                self.parent.annotation_table.remove_annotation(annotation)
            
            # 如果删除的是当前选中的标注，清除选中状态
            if self.current_annotation == annotation:
                self.current_annotation = None
                self.current_annotation_changed.emit(None)
            
            # 发出信号
            self.annotation_deleted.emit(annotation)
            
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(f"已删除标注 #{annotation_id_to_delete}", 2000)
                
        except Exception as e:
            logger.exception(f"删除标注 #{annotation.annotation_id} 时出错: {e}")
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(f"删除标注失败: {str(e)}", 2000)

    def delete_current_annotation(self):
        """删除当前选中的标注"""
        if not self.current_annotation:
            QMessageBox.information(self.parent, "提示", "请先选择一个标注")
            return
            
        # 保存要删除的标注引用
        annotation_to_delete = self.current_annotation
        
        # 清除当前选中状态
        self.current_annotation = None
        self.current_annotation_changed.emit(None)
        
        # 清除属性编辑器
        if hasattr(self.parent, 'property_editor'):
            self.parent.property_editor.set_annotation(None, None, None)
        
        # 删除标注及其关联的OCR结果
        self.delete_annotation(annotation_to_delete)

    def clear_annotations(self, show_empty_message=True):
        """清除所有标注"""
        if not self.annotations:
            if show_empty_message and hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage("没有标注可清除", 2000)
            return

        try:
            # 从场景中移除所有标注
            if hasattr(self.parent, 'graphics_scene'):
                for annotation in self.annotations:
                    try:
                        self.parent.graphics_scene.removeItem(annotation)
                    except Exception as e:
                        print(f"移除标注时出错: {e}")

            # 清空标注列表
            self.annotations.clear()
            
            # 清除当前选中的标注
            self.current_annotation = None
            self.current_annotation_changed.emit(None)
            
            # 清空标注表格
            if hasattr(self.parent, 'annotation_table'):
                self.parent.annotation_table.clear_annotations()
            
            # 清除属性编辑器
            if hasattr(self.parent, 'property_editor'):
                self.parent.property_editor.set_annotation(None, None, None)
            
            # 如果是多页PDF，也要清除当前页的数据
            if hasattr(self.parent, 'pdf_file_path') and self.parent.pdf_file_path and hasattr(self.parent, 'annotations_by_page'):
                if hasattr(self.parent, 'current_pdf_page'):
                    self.parent.annotations_by_page[self.parent.current_pdf_page] = []
            
            if show_empty_message and hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage("已清除所有标注", 2000)
                
        except Exception as e:
            print(f"清除标注时出错: {e}")

    def refresh_annotation_list(self):
        """刷新标注列表"""
        if hasattr(self.parent, 'annotation_table'):
            self.parent.annotation_table.sort_annotations(self.annotations)

    def reorder_annotations(self):
        """重新排序所有气泡标注"""
        if not self.annotations:
            QMessageBox.information(self.parent, "提示", "没有标注可排序")
            return
            
        # 检查是否是多页PDF模式
        if (hasattr(self.parent, 'pdf_file_path') and self.parent.pdf_file_path and 
            hasattr(self.parent, 'pdf_page_count') and self.parent.pdf_page_count > 1):
            
            # 多页PDF模式，询问用户选择排序范围
            reply = QMessageBox.question(
                self.parent, "重新排序", 
                "检测到多页PDF文档。请选择排序范围：\n\n"
                "是(Y) - 仅重排当前页面，保持与前面页面的连续性\n"
                "否(N) - 重排所有页面的标注\n"
                "取消 - 取消操作",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Cancel:
                return
            elif reply == QMessageBox.Yes:
                # 仅重排当前页面，保持连续性
                self._reorder_current_page_with_continuity()
                return
            else:
                # 重排所有页面
                self._reorder_all_pdf_pages()
                return
                
        # 普通排序
        self._reorder_current_page(start_id=1)

    def _reorder_current_page(self, start_id=1):
        """重排序当前页面的标注"""
        if not self.annotations:
            return
            
        # 按位置排序（从上到下，从左到右）
        sorted_annotations = sorted(
            self.annotations, 
            key=lambda ann: (
                int(ann.pos().y() // BUBBLE_REORDER_GRID_SIZE),  # 行
                int(ann.pos().x() // BUBBLE_REORDER_GRID_SIZE)   # 列
            )
        )
        
        # 重新分配ID
        current_selected_id = None
        if self.current_annotation:
            current_selected_id = self.current_annotation.annotation_id
            
        for i, annotation in enumerate(sorted_annotations):
            new_id = start_id + i
            old_id = annotation.annotation_id
            annotation.annotation_id = new_id
            annotation.update_annotation_id_display()
            
            # 如果这是当前选中的标注，更新选中状态
            if old_id == current_selected_id:
                self.current_annotation = annotation
        
        # 更新计数器
        self.annotation_counter = start_id + len(sorted_annotations) - 1
        
        # 刷新标注列表
        self.refresh_annotation_list()
        
        # 重新选中之前选中的标注
        if current_selected_id is not None:
            for annotation in sorted_annotations:
                if annotation == self.current_annotation:
                    self.select_annotation_by_id(annotation.annotation_id)
                    break
        
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(
                f"已成功重新排序 {len(sorted_annotations)} 个气泡标注（从上到下，从左到右）", 3000
            )
        
        # 发出信号
        self.annotations_reordered.emit()

    def _reorder_current_page_with_continuity(self):
        """重新排序当前页面，保持与前面页面的连续性"""
        # 计算前面页面的标注数量总和
        previous_annotations_count = 0
        
        if (hasattr(self.parent, 'pdf_file_path') and self.parent.pdf_file_path and 
            hasattr(self.parent, 'current_pdf_page') and self.parent.current_pdf_page > 0):
            if hasattr(self.parent, 'annotations_by_page'):
                for page_idx in range(self.parent.current_pdf_page):
                    page_annotations = self.parent.annotations_by_page.get(page_idx, [])
                    previous_annotations_count += len(page_annotations)
        
        # 计算起始ID
        start_id = previous_annotations_count + 1
        
        # 执行排序
        self._reorder_current_page(start_id=start_id)
        
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(
                f"已重新排序当前页面标注，起始编号: {start_id}，保持与前面页面的连续性", 3000
            )

    def _reorder_all_pdf_pages(self):
        """全局重排序所有PDF页面"""
        if not hasattr(self.parent, 'pdf_file_path') or not self.parent.pdf_file_path:
            return

        try:
            # 保存当前页
            current_page_before_reorder = getattr(self.parent, 'current_pdf_page', 0)
            
            # 1. 保存当前页的任何未保存的更改
            if hasattr(self.parent, 'save_current_page_data'):
                self.parent.save_current_page_data()
            
            # 2. 收集所有页面的标注
            all_annotations_by_page = {}
            
            # 收集已保存的页面数据
            if hasattr(self.parent, 'annotations_by_page'):
                for page_idx, page_data in self.parent.annotations_by_page.items():
                    if page_data:  # 只处理有数据的页面
                        all_annotations_by_page[page_idx] = page_data
            
            # 3. 按页面顺序重新分配ID
            global_id_counter = 1
            
            for page_idx in sorted(all_annotations_by_page.keys()):
                page_annotations = all_annotations_by_page[page_idx]
                
                # 按位置排序当前页面的标注
                sorted_page_annotations = sorted(
                    page_annotations,
                    key=lambda ann_data: (
                        int((ann_data.get('pos_y', 0) if isinstance(ann_data, dict) else ann_data.pos().y()) // BUBBLE_REORDER_GRID_SIZE),
                        int((ann_data.get('pos_x', 0) if isinstance(ann_data, dict) else ann_data.pos().x()) // BUBBLE_REORDER_GRID_SIZE)
                    )
                )
                
                # 重新分配ID
                for ann_data in sorted_page_annotations:
                    if isinstance(ann_data, dict):
                        ann_data['annotation_id'] = global_id_counter
                    else:
                        ann_data.annotation_id = global_id_counter
                    global_id_counter += 1
                
                # 更新页面数据
                all_annotations_by_page[page_idx] = sorted_page_annotations
            
            # 4. 保存更新后的数据
            if hasattr(self.parent, 'annotations_by_page'):
                self.parent.annotations_by_page.update(all_annotations_by_page)
            
            # 5. 更新全局计数器
            self.annotation_counter = global_id_counter - 1
            
            # 6. 重新加载当前页面以反映更改
            if hasattr(self.parent, 'load_pdf_page'):
                self.parent.load_pdf_page(current_page_before_reorder, skip_save=True)
            
            if hasattr(self.parent, 'status_bar'):
                self.parent.status_bar.showMessage(
                    f"已完成全局重排序，共处理 {global_id_counter-1} 个标注", 3000
                )
            
            QMessageBox.information(
                self.parent, "重排序完成", 
                f"已成功重新排序所有页面的标注\n共处理 {global_id_counter-1} 个标注"
            )
            
        except Exception as e:
            logger.exception("全局重排序过程中发生严重错误")
            QMessageBox.warning(
                self.parent, "重排序出错", 
                f"全局重排序过程中发生意外错误: {str(e)}\n\n建议重启程序。"
            )
            # 尝试恢复到原始页面
            if hasattr(self.parent, 'load_pdf_page'):
                self.parent.load_pdf_page(current_page_before_reorder, skip_save=True)

    def audit_current_annotation(self):
        """审核当前标注"""
        if not self.current_annotation:
            # 如果没有选中的，尝试选中第一个未审核的
            sorted_annotations = sorted(self.annotations, key=lambda ann: ann.annotation_id)
            first_unaudited = next((ann for ann in sorted_annotations if not ann.is_audited), None)
            if first_unaudited:
                self.select_annotation_by_id(first_unaudited.annotation_id)
            else:
                QMessageBox.warning(self.parent, "提示", "没有需要审核的标注。")
            return

        # 1. 审核当前项
        self.current_annotation.set_audited(True)

        # 2. 寻找下一个未审核项
        sorted_annotations = sorted(self.annotations, key=lambda ann: ann.annotation_id)
        current_index = -1
        for i, ann in enumerate(sorted_annotations):
            if ann.annotation_id == self.current_annotation.annotation_id:
                current_index = i
                break

        next_annotation_to_select = None
        
        # 从当前项之后开始寻找
        if current_index != -1:
            for i in range(current_index + 1, len(sorted_annotations)):
                if not sorted_annotations[i].is_audited:
                    next_annotation_to_select = sorted_annotations[i]
                    break
        
        # 如果后面没有，就从头开始找
        if not next_annotation_to_select:
            for ann in sorted_annotations:
                if not ann.is_audited:
                    next_annotation_to_select = ann
                    break
        
        # 3. 跳转
        if next_annotation_to_select:
            self.select_annotation_by_id(next_annotation_to_select.annotation_id)
        else:
            QMessageBox.information(self.parent, "审核完成", "所有标注均已审核！")

    def change_annotation_size(self, percent: int):
        """更改所有注释的大小（基于全局统一设置）"""
        if not self.annotations:
            return
            
        # 计算比例因子
        scale_factor = percent / 100.0
        
        # 更新所有标注的大小
        updated_count = 0
        for annotation in self.annotations:
            try:
                # 设置新的比例因子
                annotation.scale_factor = scale_factor
                annotation._update_geometry()
                annotation.update()
                updated_count += 1
            except Exception as e:
                print(f"更新标注 {annotation.annotation_id} 大小时出错: {e}")
        
        # 更新下一个标注的比例因子
        self.next_annotation_scale = scale_factor
        
        # 如果有当前选中的标注，更新属性编辑器
        if self.current_annotation and hasattr(self.parent, 'property_editor'):
            self.parent.property_editor.update_preview()
            
        if hasattr(self.parent, 'status_bar'):
            self.parent.status_bar.showMessage(f"已更新 {updated_count} 个气泡的大小为 {percent}%", 3000)

    def change_current_annotation_text(self, new_text: str):
        """修改当前选中标注的文本内容"""
        if self.current_annotation and self.current_annotation.text != new_text:
            self.current_annotation.set_text(new_text)
            return True
        return False

    def change_current_annotation_style(self, style_text: str):
        """修改当前标注的样式"""
        if self.current_annotation and style_text != "自定义":
            new_style = STYLE_NAME_REVERSE_MAP.get(style_text, "default")
            self.current_annotation.change_style(new_style)

    def change_current_annotation_shape(self, shape_text: str):
        """修改当前标注的形状"""
        if self.current_annotation:
            shape_map = {
                "空心圆": "circle", 
                "实心圆": "solid_circle", 
                "五角星": "pentagram", 
                "三角形": "triangle"
            }
            new_shape = shape_map.get(shape_text, "circle")
            self.current_annotation.change_shape(new_shape)
