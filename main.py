#!/usr/bin/env python3
"""
IntelliAnnotate - 智能图纸标注工具 (集成EasyOCR)

Requirements:
PySide6>=6.0.0
Pillow>=9.0.0
PyMuPDF>=1.20.0
ezdxf>=1.0.0
easyocr>=1.7.0
opencv-python>=4.8.0
numpy>=1.24.0
torch>=2.0.0
torchvision>=0.15.0

一个功能完备的2D机械图纸标注应用，支持多种图纸格式加载、
使用EasyOCR进行真实的图纸文字识别、可交互的气泡标注和实时属性编辑。
专为机械制造业紧固件图纸设计。
"""
# -- coding: utf-8 --
from PySide6.QtWidgets import QMessageBox
import platform
from datetime import datetime
import random
from ctypes import *
import os
dir_path = os.path.dirname(os.path.realpath(__file__))
if 'Windows' in platform.system():
    if "32bit" in platform.architecture():
        Psyuunew=windll.LoadLibrary(dir_path+'/Syunew3D.dll')
    else:
        Psyuunew=windll.LoadLibrary(dir_path+'/Syunew3D_x64.dll')
else:
    if "32bit" in platform.architecture():
        Psyuunew=windll.LoadLibrary(dir_path+'/libPsyunew3.so')
    else:
       Psyuunew=cdll.LoadLibrary(dir_path+'/libPsyunew3_64.so')          

##�����İ汾
NT_GetIDVersion=Psyuunew.NT_GetIDVersion
NT_GetIDVersion.argtypes=(c_void_p,c_char_p)
NT_GetIDVersion.restypes=(c_int)


##��ȡ������չ�汾
NT_GetVersionEx=Psyuunew.NT_GetVersionEx
NT_GetVersionEx.argtypes=(c_void_p,c_char_p)
NT_GetVersionEx.restypes=(c_int)


##�㷨����
sWrite_2Ex=Psyuunew.sWrite_2Ex  
sWrite_2Ex.argtypes=(c_ulong ,c_void_p,c_char_p)
sWrite_2Ex.restypes=(c_int)

sWriteEx=Psyuunew.sWriteEx  
sWriteEx.argtypes=(c_ulong ,c_void_p,c_char_p)
sWriteEx.restypes=(c_int)

sRead=Psyuunew.sRead  
sRead.argtypes=(c_void_p,c_char_p)
sRead.restypes=(c_int)

sWrite_2=Psyuunew.sWrite  
sWrite_2.argtypes=(c_ulong ,c_char_p)
sWrite_2.restypes=(c_int)

sWrite_2=Psyuunew.sWrite_2  
Psyuunew.argtypes=(c_ulong ,c_char_p)
sWrite_2.restypes=(c_int)
##�㷨����

##дһ���ֽڵ���������
YWrite=Psyuunew.YWrite
YWrite.argtypes=(c_byte ,c_short,c_char_p ,c_char_p,c_char_p )
YWrite.restypes=(c_int)

##�Ӽ������ж�ȡһ����??
YRead=Psyuunew.YRead
YRead.argtypes=(c_void_p,c_short,c_char_p ,c_char_p,c_char_p )
YRead.restypes=(c_int)

##дһ���ֽڵ���������
YWriteEx=Psyuunew.YWriteEx
YWriteEx.argtypes=(c_void_p,c_short,c_short,c_char_p,c_char_p,c_char_p )
YWriteEx.restypes=(c_int)

##�Ӽ������ж�ȡһ����??
YReadEx=Psyuunew.YReadEx
YReadEx.argtypes=(c_void_p,c_short,c_short,c_char_p,c_char_p,c_char_p )
YReadEx.restypes=(c_int)

##����ָ���ļ�������ʹ����ͨ�㷨һ??
FindPort_2=Psyuunew.FindPort_2  
FindPort_2.argtypes=(c_int ,c_ulong ,c_ulong ,c_char_p)
FindPort_2.restypes=(c_int)

##���Ҽ���??
FindPort=Psyuunew.FindPort  
FindPort.argtypes=(c_int ,c_char_p)
FindPort.restypes=(c_int)

##��ȡ����ID
GetID=Psyuunew.GetID  
GetID.argtypes=(c_void_p,c_void_p,c_char_p)
GetID.restypes=(c_int)

##�Ӽ������ж��ַ�??
YReadString=Psyuunew.YReadString 
YReadString.argtypes=(c_char_p ,c_short,c_int ,c_char_p ,c_char_p,c_char_p)
YReadString.restypes=(c_int)

##д�ַ�����������??
YWriteString=Psyuunew.YWriteString
YWriteString.argtypes=(c_char_p,c_short,c_char_p ,c_char_p,c_char_p )
YWriteString.restypes=(c_int)

##����д��??
SetWritePassword=Psyuunew.SetWritePassword
SetWritePassword.argtypes=(c_char_p ,c_char_p,c_char_p ,c_char_p,c_char_p)
SetWritePassword.restypes=(c_int)

##���ö���??
SetReadPassword=Psyuunew.SetReadPassword
SetReadPassword.argtypes=(c_char_p ,c_char_p,c_char_p ,c_char_p,c_char_p)
SetReadPassword.restypes=(c_int)

##������ǿ�㷨��Կһ
SetCal_2=Psyuunew.SetCal_2
SetCal_2.argtypes=(c_char_p,c_char_p)
SetCal_2.restypes=(c_int)

##ʹ����ǿ�㷨һ���ַ������м���
EncString=Psyuunew.EncString  
EncString.argtypes=(c_char_p,c_char_p,c_char_p)
EncString.restypes=(c_int)

##ʹ����ǿ�㷨һ�Զ��������ݽ��м���
Cal=Psyuunew.Cal  
Cal.argtypes=(c_void_p,c_void_p,c_char_p)
Cal.restypes=(c_int)

##������ǿ�㷨��Կ??
SetCal_New=Psyuunew.SetCal_New
SetCal_New.argtypes=(c_char_p,c_char_p)
SetCal_New.restypes=(c_int)

##ʹ����ǿ�㷨�����ַ������м�??
Cal_New=Psyuunew.Cal_New  
Cal_New.argtypes=(c_void_p,c_void_p,c_char_p)
Cal_New.restypes=(c_int)

##ʹ����ǿ�㷨�����ַ������м�??
EncString_New=Psyuunew.EncString_New  
EncString_New.argtypes=(c_char_p,c_char_p,c_char_p)
EncString_New.restypes=(c_int)

##�������ĳ�������
GetProduceDate=Psyuunew.GetProduceDate  
GetProduceDate.argtypes=(c_char_p,c_char_p)
GetProduceDate.restypes=(c_int)

##����ID����
SetID=Psyuunew.SetID
SetID.argtypes=(c_char_p ,c_char_p)
SetID.restypes=(c_int)

##������ͨ��??
SetCal=Psyuunew.SetCal
SetCal.argtypes=(c_char_p ,c_char_p,c_char_p ,c_char_p,c_char_p)
SetCal.restypes=(c_int)

##��������ת������
##SnToProduceDate=Psyuunew.SnToProduceDate
##SnToProduceDate.argtypes=(c_char_p ,c_char_p )
##SnToProduceDate.restypes=(c_void)

##ʹ����ǿ�㷨���ַ������н���ʹ������
##StrDec=Psyuunew.StrDec
##StrDec.argtypes=(c_char_p,c_char_p,c_char_p)
##StrDec.restypes=(c_void )
##
##StrEnc=Psyuunew.StrEnc  
##StrEnc.argtypes=(c_char_p,c_char_p,c_char_p)
##StrEnc.restypes=(c_void)
##
##EnCode=Psyuunew.EnCode    
##EnCode.argtypes=(c_void_p ,c_void_p ,  c_char_p )
##EnCode.restypes=(c_void)
##
##DeCode=Psyuunew.DeCode   
##DeCode.argtypes=(c_void_p , c_void_p , c_char_p  )
##DeCode.restypes=(c_void)
##ʹ����ǿ�㷨���ַ������н���ʹ������)


##ʹ����ǿ�㷨�Զ��������ݽ��м���ʹ������)
##DecBySoft=Psyuunew.DecBySoft         
##DecBySoft.argtypes=(c_void_p, c_void_p )

##EncBySoft=Psyuunew.EncBySoft         
##EncBySoft.argtypes=(c_void_p   ,  c_void_p   )
##ʹ����ǿ�㷨�Զ��������ݽ��м���ʹ������)

##�ַ��������������ݵ�ת��
##HexStringToc_byteArray=Psyuunew.HexStringToc_byteArray
##HexStringToc_byteArray.argtypes=(c_char_p ,c_void_p)
##HexStringToc_byteArray.restypes=(c_void)
##
##ByteArrayToHexString=Psyuunew.ByteArrayToHexString
##ByteArrayToHexString.argtypes=(c_void_p,c_char_p ,c_int )
##ByteArrayToHexString.restypes=(c_void)
##�ַ��������������ݵ�ת��

 ##��ʼ��������,ע�⣬��ʼ���������е�����??����д����Ҳ??0000000-00000000����ǿ�㷨���ᱻ��ʼ??
ReSet=Psyuunew.ReSet
ReSet.argtypes=[c_char_p]
ReSet.restypes=(c_int)

##���º���ֻ���ڴ�U�̵�??
##�����Ƿ���ʾU�̲����̷�����Ϊ��ʾ����Ϊ����ʾ
SetHidOnly=Psyuunew.SetHidOnly 
SetHidOnly.argtypes=( c_bool,c_char_p)
SetHidOnly.restypes=(c_int)

##����U�̲���Ϊֻ��״̬��
SetUReadOnly=Psyuunew.SetUReadOnly 
SetUReadOnly.argtypes=[c_char_p]
SetUReadOnly.restypes=(c_int)
##���Ϻ���ֻ���ڴ�U�̵�??

##���º���ֻ֧������оƬ��??
##����SM2��Կ??
YT_GenKeyPair=Psyuunew.YT_GenKeyPair
YT_GenKeyPair.argtypes=(c_char_p ,c_char_p,c_char_p,c_char_p)
YT_GenKeyPair.restypes=(c_int)

##����Pin??
YtSetPin=Psyuunew.YtSetPin
YtSetPin.argtypes=(c_char_p,c_char_p,c_char_p )
YtSetPin.restypes=(c_int)

##����SM2��Կ�Լ�����
Set_SM2_KeyPair=Psyuunew.Set_SM2_KeyPair
Set_SM2_KeyPair.argtypes=(c_char_p,c_char_p,c_char_p,c_char_p,c_char_p )
Set_SM2_KeyPair.restypes=(c_int)

##���ؼ������Ĺ�Կ
Get_SM2_PubKey=Psyuunew.Get_SM2_PubKey
Get_SM2_PubKey.argtypes=(c_char_p,c_char_p,c_char_p,c_char_p)
Get_SM2_PubKey.restypes=(c_int)

##�Զ��������ݽ���SM2����
SM2_EncBuf=Psyuunew.SM2_EncBuf
SM2_EncBuf.argtypes=(c_void_p,c_void_p,c_int ,c_char_p)
SM2_EncBuf.restypes=(c_int)

##�Զ��������ݽ���SM2����
SM2_DecBuf=Psyuunew.SM2_DecBuf
SM2_DecBuf.argtypes=(c_void_p,c_void_p,c_int ,c_char_p ,c_char_p)
SM2_DecBuf.restypes=(c_int)

##���ַ�������SM2����
SM2_EncString=Psyuunew. SM2_EncString
SM2_EncString.argtypes=(c_char_p,c_char_p,c_char_p)
SM2_EncString.restypes=(c_int)

##���ַ�������SM2����
SM2_DecString=Psyuunew.SM2_DecString
SM2_DecString.argtypes=(c_char_p,c_char_p,c_char_p ,c_char_p)
SM2_DecString.restypes=(c_int)

##����Ϣ����SM2ǩ��
YtSign=Psyuunew.YtSign
YtSign.argtypes=(c_char_p , c_char_p  ,c_char_p ,c_char_p)
YtSign.restypes=(c_int)

##��SM2ǩ��������ǩ
YtVerfiy=Psyuunew.YtVerfiy
YtVerfiy.argtypes=(c_char_p,c_char_p,c_char_p,c_char_p,c_char_p,c_void_p,c_char_p)
YtVerfiy.restypes=(c_int)

##SM2�㷨��ʼ��(ʹ������)
IniSM2=Psyuunew.IniSM2
IniSM2.restypes=(c_int)

##���ַ�������SM2����(ʹ������)
SM2EncString=Psyuunew.SM2EncString
SM2EncString.argtypes=(c_char_p,c_char_p,c_char_p ,c_char_p)
SM2EncString.restypes=(c_int)


##���ַ�������SM2���ܣ�ʹ��������
SM2DecString=Psyuunew.SM2DecString
SM2DecString.argtypes=(c_char_p,c_char_p,c_char_p)
SM2DecString.restypes=(c_int)

##����Ϣ����SM2ǩ����ʹ��������
SM2Sign=Psyuunew.SM2Sign
SM2Sign.argtypes=(c_char_p , c_char_p ,c_int ,c_char_p ,c_char_p)
SM2Sign.restypes=(c_int)

##��SM2ǩ��������ǩ��ʹ��������
SM2Verfiy=Psyuunew.SM2Verfiy
SM2Verfiy.argtypes=(c_char_p,c_char_p,c_char_p,c_char_p,c_char_p)
SM2Verfiy.restypes=(c_int)

##�ͷ�SM2�㷨(ʹ������)
ReleaseSM2=Psyuunew.ReleaseSM2

##��������Ӳ��оƬΨһID
GetChipID=Psyuunew.GetChipID 
GetChipID.argtypes=(c_char_p,c_char_p)
GetChipID.restypes=(c_int)
##���Ϻ���ֻ֧������оƬ����

##���º���ֻ֧��D8
CheckDate=Psyuunew.CheckDate
CheckDate.argtypes=(c_char_p,c_char_p)
CheckDate.restypes=(c_int)
##���Ϻ���ֻ֧��D8


if 'Linux' in platform.system():
	CloseUsbHandle=Psyuunew.CloseUsbHandle
	CloseUsbHandle.argtype=c_char_p
	CloseUsbHandle.restypes=(c_void_p)

def CheckDateEx(Path):
    dt=datetime.now()
    nowDate=dt.strftime( '%Y/%m/%d/%H/%M/%S' )
    ret=CheckDate(nowDate.encode('utf-8'),Path)
    return ret

def HexStringToByteArrayEx(InString):
    
    mylen=len(InString)
    array_data={}
    in_data=c_byte
    temp=''
    for n in range(0,mylen,2):
        temp=InString[n:2+n]
        temp='0x'.encode()+temp
        in_data=int(temp,16)
        array_data[n/2]=(in_data)
    return array_data

def StringToByteArray(InString):
    
    mylen=len(InString)
    array_data={}
    in_data=c_int
    temp=''
    for n in range(0,mylen):
        temp=InString[n:1+n]
        in_data=ord(temp)
        array_data[n]=(in_data)
    array_data[n+1]=0
    return array_data

def ByteArrayToString(InBuf):
    arrBytes = bytearray()
    for n in range(0,len(InBuf)):
        arrBytes.append(InBuf[n])
    return arrBytes.decode()
    

def ByteArrayToHexString(in_data,inlen):
    OutString=''
    temp=''
    for n in range(0,inlen):
        temp='%02X' % in_data[n]
        OutString=OutString+temp
    return OutString

def EnCode(InData,Key,pos):
    KeyBuf=HexStringToByteArrayEx(Key)
    OutData=EncBySoft(InData,KeyBuf,pos)	
    return OutData

def DeCode(InData,Key,pos):
    KeyBuf=HexStringToByteArrayEx(Key)
    OutData=DecBySoft(InData,KeyBuf,pos)
    return OutData


def EncBySoft(inb, KeyBuf,pos):
    bufArray=c_uint32*16
    buf=bufArray

    temp_string=''
    cnDelta = 2654435769
    _sum = 0
    a = 0
    b = 0
    c = 0
    d = 0
 
    for n in range(0,4):
        a = (KeyBuf[n] << (n * 8)) | a
        b = (KeyBuf[n + 4] << (n * 8)) | b
        c = (KeyBuf[n + 4 + 4] << (n * 8)) | c
        d = (KeyBuf[n + 4 + 4 + 4] << (n * 8)) | d 
    y = 0
    z = 0

    for n in range(0,4):
        temp = (inb[pos +n])
        y = (temp << (n * 8)) | y
        temp = (inb[pos +n + 4])
        z = (temp << (n * 8)) | z

    n = 32
    while  n > 0:
        _sum = (cnDelta + _sum) & 0xffffffff
        temp=(z << 4) & 0xffffffff
        temp=(temp+a)& 0xffffffff
        temp_1= (z + _sum) & 0xffffffff
        temp_2=((z >> 5) + b)& 0xffffffff
        temp=temp ^ temp_1 ^ temp_2
        y = (y+ temp)& 0xffffffff
        temp=(y << 4)& 0xffffffff
        temp=(temp + c)& 0xffffffff
        temp_1=(y + _sum)& 0xffffffff
        temp_2=((y >> 5) + d)& 0xffffffff
        temp=temp ^ temp_1 ^ temp_2
        z=(z+temp)& 0xffffffff
        n = n - 1

    outb={}
    for n in range(0,4):
        outb [n]= (y >> (n) * 8) & 255
        outb[n + 4] = (z >> (n) * 8) & 255

    return outb

def DecBySoft(inb, KeyBuf,pos):
    bufArray=c_uint32*16
    buf=bufArray
    temp_string=''
    cnDelta = 2654435769
    _sum = 0xC6EF3720
    a = 0
    b = 0
    c = 0
    d = 0
    for n in range(0,4):
        a = (KeyBuf[n] << (n * 8)) | a
        b = (KeyBuf[n + 4] << (n * 8)) | b
        c = (KeyBuf[n + 4 + 4] << (n * 8)) | c
        d = (KeyBuf[n + 4 + 4 + 4] << (n * 8)) | d
    y = 0
    z = 0
    for n in range(0,4):
        temp = (inb[pos +n])
        y = (temp << (n * 8)) | y
        temp = inb[pos +n + 4]
        z = (temp << (n * 8)) | z

    n = 32
    while  n > 0:
        temp=(y << 4)
        temp= ( temp+ c) & 0xffffffff
        temp_1=(y + _sum)& 0xffffffff
        temp_2=((y >> 5) + d)& 0xffffffff
        temp= temp ^ temp_1 ^ temp_2
        z=(z-temp) & 0xffffffff
        #z -= ((y << 4) + c) ^ (y + _sum) ^ ((y >> 5) + d)
        temp=(z << 4)& 0xffffffff
        temp=(temp+a)& 0xffffffff
        temp_1=(z + _sum)& 0xffffffff
        temp_2=((z >> 5) + b)& 0xffffffff
        temp= temp ^ temp_1 ^ temp_2
        y = (y -temp)& 0xffffffff
        _sum = (_sum -cnDelta)& 0xffffffff
        n = n - 1
    
    outb={}
    for n in range(0,4):
        outb[n] = (y >> (n) * 8) & 255
        outb[n + 4] = (z >> (n) * 8) & 255

    return outb

def StrDec(InString,Key):
   OutBuf={}
   mylen=len(InString)/2
   KeyBuf=HexStringToByteArrayEx(Key)
   InBuf=HexStringToByteArrayEx(InString)
   for n in range(0,(mylen-8)+1,8):
        tempBuf=DecBySoft(InBuf,KeyBuf,n)
        for i in range(0,8):
             OutBuf[i+ n] = tempBuf[i]
   if mylen>8:
       n=len(OutBuf)-1
       for n in range(len(OutBuf),mylen):
            OutBuf[n]=(InBuf[n])
   return ByteArrayToString(OutBuf)


def StrEnc(InString,Key):
    OutBuf={}
    InBuf={}
    temp_Buf=InString.encode('utf-8')
    mylen=len(InString)+1
    for n in range(0,mylen-1):
        InBuf[n]=temp_Buf[n]
    InBuf[n+1]=(0)
    if mylen<8:
            for n in range(mylen,8):
                 InBuf[n]=(0)
            mylen=8
            
    KeyBuf=HexStringToByteArrayEx(Key)
    for n in range(0,(mylen-8)+1,8):
         tempBuf=EncBySoft(InBuf,KeyBuf,n)
 
         for i in range(0,8):
              OutBuf[i+ n] = tempBuf[i]
    if mylen>8:
       n=len(OutBuf)-1
       for n in range(len(OutBuf),mylen-1):
            OutBuf[n]=(InBuf[n])
       OutBuf[n+1]=0
    OutString=ByteArrayToHexString(OutBuf,mylen)
    return OutString

#ʹ����ǿ�㷨������Ƿ���ڶ�Ӧ�ļ�����
def CheckKeyByEncstring_New():
    DevicePath=create_string_buffer(260)
    EncInString=["24372","27125","15359","9977","6859","6427","10456","4501","26492","11627","7343","17338","18453","18057","16540","10778","15998","1719","13786","18694","1473","25814","8132","7793","27656","7786","19923","9197","19613","18834",  \
"1441","28843","10730","1811","27879","31613","26489","21580","4994","31959","9205","32066","7530","28873","13095","11925","3256","14598","14329","10120","11175","24287","24180","3069","5778","30023","13764","22132","26097","6865",  \
"10803","27154","30816","7084","7977","17560","23704","13228","10545","20582","23412","28738","26613","22145","8684","12929","65","4234","2844","20763","13819","23233","2620","23137","9776","1547","13480","30278","8457","18920",  \
"27608","26716","9168","22138","10089","12756","29423","1669","24337","21377","21488","12649","5294","12245","26493","7464","32310","3297","9058","4198","20423","13912","27781","1123","9625","16267","20723","30464","3590","25965",  \
"25045","23746","13191","4240","11637","13002","9912","30500","22845","12506","18514","24318","23479","6995","21295","32655","22158","13623","1610","1001","4235","21033","28347","11452","3992","2156","20926","4701","21182","735",  \
"13995","1724","11305","29445","4350","5995","15138","8963","2640","12189","1556","10135","27739","23076","13447","12837","11001","9074","26918","687","3621","7064","15292","31189","31734","30792","21298","19164","25258","22230",  \
"8082","19877","17049","7730","13318","28848","22589","239","16600","19234","26160","7998","11634","20156","7980","18674","21086","6623","12299","24444","21319","27021","27079","22511","6390","11114","2939","23805","27476","12213",  \
"11359","4344","11117","31570","17871","28624","11626","14788","23876","20762","12949","20266","20056","2567","857","2633","13509","12135","10558","30336","30308","30638","18671","19924","27954","15599","7223","2790","12615","7898",  \
"3071","6903","733","17601","27216","12265","21581","16279","19542","12863","29714","3054","1115","21274","22215","13303","15303","15934","294","4485","11557","27659","4615","12297","32287","4996","27749","20118","14720","15434",  \
"347","479","26928","28831","14881","30258","20539","3943","531","20870","17610","15906","28841","13673","10792","31474","4992","24847","2749","20123","22606","29514","21012","7805","5873","19596","9944","7835","15200","4326",  \
"25809","25493","4023","18955","14059","5297","9774","10572","20783","15821","25376","27036","19143","16749","9992","20877","6141","27314","6271","21840","9275","8032","3648","22712","3084","12759","6435","4407","30487","21273",  \
"11811","2014","4651","9418","28762","15199","10541","27433","6915","25101","24874","24347","2935","19419","4683","8224","27439","25688","16858","18011","31174","21688","15425","5261","30456","17869","20235","18969","11420","16905",  \
"8928","5377","6318","14205","13708","9190","14150","6620","29687","22538","27807","9336","29741","5969","3860","15425","20311","15931","4382","29132","7104","12779","19287","9033","1744","4643","26232","16011","3858","2884",  \
"25950","8964","24112","18512","20224","9157","17141","1258","30841","10625","19274","16212","28420","29832","32312","2672","506","32463","13804","32702","27860","32231","6573","20835","8495","1282","14310","31775","12482","18161",  \
"16417","6486","22376","30758","23817","20743","9499","20660","11879","27606","18421","11853","25380","1285","30257","22082","29516","23669","30759","22406","31240","18601","21075","3243","12194","18627","3919","15842","901","13956",  \
"10707","25342","22073","6224","5818","1393","21353","4902","28413","16972","27615","31914","14777","27713","28550","3358","22054","19876","18279","12643","1438","2661","20396","31996","5531","8727","20895","15371","10678","4587",  \
"7595","9554","9643","5812","17752","11313","14163","3812","30033","29357","169","31883","28513","23398","25029","10078","9329","15263","25314","24237"]
    EncOutString=["33C78946FE3ECAEF","14C4688892011997","14AF5F0EC0DD8455","B5007A25F9A87530","4EC7C0DDD01755DF","310C4B8F874B2F69","3DA54590DDDADA7C","D91F819EC0F187CE","C2BAA558B690930D","C6F53F1AE9094D10","79045607F8366E20","795A46487A4D7C60","02245AC7EF3E760F","9B838FEDBF795064","27E8E72069B7A006","DB37365FC34B6794","F5CB55EAD5471CE9","78EB487E3294C79B","38D4BBB1139F7263","72AB6312CDCDDF34","698C64D00BDB0639","C04D8F50FFB1EF30","A57389C24EFD77D7","69994A9280701F82","AD66F103EE3DEF7A","F5323C14ACCFCB3C","EBE637AB3CFAD28C","25F1EA866DF39D6F","05AA362C101439BF","4CECED948FBC73BE",  \
"C868B7BC48B14116","21F24DA6FF79379A","9CDD01F9FB5DCA74","CC28A7793E28F9C8","C30AA49C53915909","8F9997BBBCD7E599","7B9DBE105F0B2340","690E91BAA79C5E63","3CBF90E5658943BA","20507320ED949D31","6AFA87F38D8CA32D","DF17C86B726879A6","FB0C00D8B393A8CF","2BA9FEA3C35367E5","F6952381C6EB8FC0","0352FCF199D3DDFA","135B8E0C499A1624","B8512AC9821E6C39","6DFFDEA6A4EFC4D9","4B145F0DE2330579","ED7201BC65724CF0","2BBB66590CCAB506","E49BE78DD0F6FB25","8420FD52CB68025B","6993113A807F307E","23343545FA4B5DB3","47C14181AF540DF3","23CE84EC0DC1F0A4","74ADEE5CF693F165","582B9000689329AA",  \
"F2662C687FC6485D","6F4970A597FC8DCF","259389728CB460A7","7496C6707EC9EBD9","8F2A7236B6E99CF0","DA57707AAE6169D7","FD53C1788382B657","B35B4708B5FA8833","321A5405E5D1C603","BCC3C0BE52CEEE30","A7CEED7754A17206","8A7DFA654E5782DA","EF160806AC610BA9","61255ABC9A5953C5","9255B8B92F149942","0FF7990FC443FC31","8A08E079B206C1D0","8B96786788F9268F","31532A8CCEB15654","3D30ADBCD62CF6F8","85B4E59004DD99D4","03F1355EA975E364","8AAFBF3D002B08AE","EF4099F8CC122FED","AE57D2C393D638B2","57949E2B125D64AA","6C685F5BB82FF408","86EC46EC63E5007C","3CE8037DD309A223","9157C35BA611DB0A",  \
"2255D77186385476","B4430766448DE314","B3750FE3704AB7DD","DC1683201BBC1FA6","EB54DA56B54531A8","48F2207E1AD8F85C","0739BFAEBA40178B","79DF83F6C6D9C8C3","C0B7A679F4D6FA0C","658D774C557A48D2","83E091DAF6830802","E02E757DFA662028","B4B0820F279689D6","380795E70C9E4D9A","0D8FCD408FC59D17","7F2DD9C5622D7185","3424002808737288","6F3665CFA356D9C4","7FCDC856A961A26E","8635823D726DE17F","D5733FFE221F1C7D","A17297BEE116FA41","867B3B4F50893741","9F2DE53F5F76348A","0526E04DFF44BACD","9933D26ACCA32B08","B484D0833E53B036","DDE5C985AB1CC6F6","592477772E66195E","FF2EAD645E3F4432",  \
"C2C512087C57B297","77CBA5235F58D1B1","A0F5F4B989468D1B","4A0B2CC54F107D1A","DCD8E3D5463D4B81","B5FC999C6461D617","9BC5042014F0A007","674980D139B25959","D5AF48957953B509","74084A598776CCE6","3520988C3BE64580","576B1ED4D6464227","5867483508741B52","543243402A2E37C2","C1E3A9818D7FAF0D","7C75A82052AC6670","D7B6A01BAEF6654F","6B38BAC2EBB03A70","664414FE61D3D762","F609F3FA6AE377B6","12E20C2D14E70A5A","74510D2B6EA317AF","6486BA393DB020A6","89E15A977A53A233","392953B5347E6F26","C2354758F2404C12","3F9F11F7A2176600","67C9982F9BC4C7C0","BD524C8CEE339693","04E541DB3E767A0A",  \
"1E2E2B0579EE7DD0","870F16C4B8EE7772","A43472163359E500","1D9CD2C25DEF0DC4","5723365E4E9DF5DD","23CCF5CA839BD9D9","15E3393B84C4DB60","40541AD9AB48FC7B","9D05D0245574A54A","6994B167E607CCD8","532CFADF1C58FC1D","704BFB8EADDB40CE","FC84255521A8F5EB","6B8D87E84A3F3011","D66CE4AA5C8923FB","60EBF454405AAA51","A2A3676A0D08E26F","4F1056AB80BE06D6","EA23B4F6BD9AF599","BFC9C61CFC443897","9641A7D43139283E","B870D7F1FD521C74","7599D6BB1EB13663","5476D715ECCEFE92","20AEA0E0364201A0","6E3C73D5B7816232","05944B257F8E8855","12C33E29A84D2324","53D0CD861ED5FB52","980A00F47A505433",  \
"133D2662B9EE8D75","B6901FD4454C8814","8206E6BB1E1E1730","7E8A7F6317B610DF","438652419D8445A1","8D08D6FA51400CF8","72EA573C1E0ABAD3","9B36D20E0684CEBB","C1846C07A8A94C6E","1901FD2474D19DB6","1709E780D542AEE6","047DDF128E149844","BD855ACED7ED9C5A","F7D0A3DC6AD9B9BA","414D686D4A9B7A86","E5B3373700C94108","DFBABA0F62BB1467","9C6722500447C583","1C1E3C32B7DE349E","E38D06FD09EC324D","CB358E7784C0748F","035ADA7F951F437A","873734A2FE62985A","B70F30D88E0A9CBB","0F2B722FB803F248","DCD5533DB49E1AFE","3AA7625B958F4594","5A367EEFB8BAC909","D27A1F04A0D8A5DC","50D311E5FDE470F0",  \
"00785D2C19197FAB","363A94F7B032D4AE","16FD4820778AD105","213E5E5D4AF5964F","B2128D6CB5024C43","659A706D87C99535","36E65FD5340AA970","09D1FDA9FA2746D8","10671B045ACB6DC5","7DECE03C5B675768","1767F89C2B365003","A4B653346CBFAC0D","BEB6B36F90D5FCEC","89BD5111921DCFBA","317D0C683B4D0D33","2AA2BD0466E852CA","AB58B9021AAD1052","165E442EE1E1C27B","8A22D018467123C2","A3851434F4EBEE3C","850F202852F1BF49","3DC1B789C8CFF025","D959B5DCF8F380C2","7553A2890316A833","ECE3CE5612D47581","604762A214505414","723BB7278E590BF0","28050940E1A64CBE","042B897864DA7077","42F02914E78EFB02",  \
"10A73E55CB9C7398","284949E0BF83904A","DEA0FA5E213481CD","B3031D7802A99808","9C6DB7EB1064D02E","F5C8498F0A603140","63A9C72902BB6FE6","3EBAA5481A4D7C47","423CADD046D69FC1","F1E086D71E4FBCFD","82707019383A06CE","7BBCCC0CA60F7C70","F2196068CD8E2DC2","54997DFE91561245","B77C314F48E8FC8B","56C4C03323F23A5C","29CEDB0FBB55A6DC","CE74C1B56E329C4C","AE830906B6B19951","99E3FFCDC2FC2C99","EB8EE9C333E911A1","21B4C02FE655A0F1","298E5F18173DF161","6501598EBD0975DA","004566BAE2ACE07C","C9DC6E3D7BF3D37F","EADACE52A5A5FD07","B388D9FB1695114F","DBD8911A8E254794","CAAB04618E2EA8C1",  \
"CF1EDE88CB1C5A52","999610AA0F5FE728","58BBCB558A9F469D","46967A673430ADDA","343CD6CD2FF6F95E","2CB2DA78BCE79F3F","54575833657C764A","C42EFCADD40364AC","C412FFD3FAA1110A","0028DB1D3D7762B1","4169FF3ED4E22508","A9CA724BD681171A","D63255D820B7D707","CE5196C40A20A755","7B65916DCD32FA17","3D0414D0D6F9C795","7213550D3893D245","0501F9C6EC1C7387","A708FFE19D60B0E2","EEDD1CB5074DC2BD","C54EEA62D5D36ADE","4DDA56E673856FB5","B00BE5F975C5B709","D84B3FE718A28C7E","4CE9C6B80E55C872","E1B978CFB2C96E41","233D8F13A28BDDC0","58CE9C256551A25B","15BDA6B16524B1C6","1D97AEE89F167D80",  \
"C75A961DA46DA45E","2EC0789CB2A90E52","24FB3D4F4F2E1335","B6A00E06ABDA19FF","235555C33ACFEA44","D80562C60FBE2900","667FC49324BC40B9","6793978168B9B435","FFA657DC4200DB90","B744C0CB9BFA87C2","60DB6D00A3D7342B","79EF7C33FDDA8AA5","FFB3748D8785CC45","0ED30DB0447C750B","B49C552C3D2D9C54","F7A97A425EBB3B77","6595D050FDF9DD41","B8621E027C27976C","1D4987A162BA157E","00DB749A2272D7D2","AC19960CD5F7DACE","8A3E98DC8CA860B4","2E5B0957C793BA4E","D7A067F13780E9FD","BEED3AEF3C51F5E2","558391D29CE9D91B","C903CD74B932BE29","D0DF578EB4BDE874","20ACBC07A1D84222","7C1F29F061CC45EA",  \
"382E9E810011A4D1","F46586500A9ECAED","8769296D542DB22A","B37A07F369E2B253","9DC32F4877389842","68D8C5420712862C","7361076905D12524","B4057C79CAB9A86C","4A0A5AC0D76D8FE3","E8AAAA6477ED026D","E88BC0CFC2230AEF","3A56B06F55D8F039","F7F2C3B52F0A9F4C","98DDDC952A20BACE","A8E2BE91C29C71E8","FC52233D503D9447","65B00A3312E2E81F","8932F4A9CED73F28","2219CF44CE4317E5","C55080BA67C1B348","FA51B10561E9AF20","E59B15DE2D9373C5","8083CDF4DEF3C90A","2B36E319C8E16A31","482B94E40E6A3DF9","75B8C5A3879258DC","F450C14F8720A48F","DDBBFC65C228DC46","79E4AC983BE15779","3E4716651E661586",  \
"7E75FF90A5C1A11C","8E7821803CAC11A7","BFBC7FCCC4609D46","4B793932BDFD94E2","355F4294A788AF96","1D99932E7BC2E079","532E2AFDDC0487DB","A85712D508992568","797DD47F4AE08D75","4851C9F8C53B877D","3ACAC41486B62646","2286243733803C49","DF77B22DB1A026A2","6EB1B278D8AE18CB","1F6CCBF4FB366EDE","8083CDF4DEF3C90A","C494369543A9F166","AA74CC7AFA911104","7A9186B52D8AB1D4","EB2F7FDFFDC1BF6F","66E10394584EC250","12D05899A6909279","1EA0285FC08C4B4D","045C40C6997E0508","A7E4EEE9E2E48AA0","3157BC6D45BD33FD","39BDFE431CD10CF3","8D9FBD5404ED8C13","87915D6347005309","49A57D76B1FA70CF",  \
"51CDD3546B3A1907","F8F61E9F5C1F7905","5D3B88BAA034A89A","F57DC2512055BE3F","53F819066208111D","26DD3B78A2C8663D","4316467C8984CCAC","B298435EE3F92DE4","3506587320821B1F","02A29536DB817348","6220EBB89657F8B3","CCF2BDBBC2B1AE99","8F8216539C436CB9","6E347FBE47305B3C","A52106B63CCCD991","FEA5E46CB54FFE4D","552EFB59F5B6A465","1972146EA53C3CA6","EBA443B6CBF4F5B0","B4CA8F12AA24B02A","30440B079FAD583E","5C80759510A86DF4","3F56F37B0189EC2A","2575C4B465257D81","9D11D47415A8C395","3E0FF3FBF13D90C9","66CF464C9882D01B","8CA87F61EF5181AB","D5CB65A1D9773073","5AF8F97034D8893B",  \
"C35CDFC5D264EFE4","ED2A39B2018A6CFF","F1119CC6F6031F95","B0CF76F103EB279F","0BED2BD79E700B34","80CD27D5619537A7","F9B1AFA5B09D2F6E","7C42A0A3DD6E96CB","C33E916C02C3E3B4","BE6DA78BD28178E8","6F9377710A4E5A02","FED5AD13D08C9133","D7293ED993FD170C","9CD5DC6B395680F4","36730D8FB9D0C206","76FE3C2956C2A9EB","05C54AD7ACFC3787","0E781C3BC0E241E7","A43B6CC77BE207EA","4718E268699DB996","181C153C4848EFD4","DB8AF2F4C6C91269","9A0DF5ECFB7D6764","8A51C66329648888","D62E47929A749C1D","459C489AA920BAB6","BEE65F11B207F747","54DAF91F5804F527","CB211EB397BB1C6A","8E32366781883CA6",  \
"C8B34373DB97B01B","9196B812A6C6DB74","E8D7B7A4EA3126A7","951121FA742B5164","BA041D5BDDD7AB93","A7683E33131B3D23","162CE706122E4268","8FA4273451BB4C9C","D74D749B79D5C07F","EEDAA6C2C9B4CC00","98C7EC861705C30D","BA42E58B5862DB30","54C0C76AB155CFC3","0D890FDAE2FDE9F2","2C63273E58D1C93B","82BF55BB7B50441B","5D87AAD857FE538D","52B580E28B6EEE1B","E038FD1263AE70AC","9A36C04D4FD73941","281B279FDCBBF89C","7836F0FC867580F4","5A6FE9435B6A0996","F89E53020DB78849","0F42E1CE1CF619F4","D5E3CAD61C3093B2","A1A6197921647C22","E53EC72FCE260C90","8A91EFB5A78ABC56","93480C296A611AAB",  \
"0F977EF6741FB3BA","1609771267B7D575","2B8031A682791472","C78AD4BC8A178EB8","0FE381798E2A8643","B527E764A0AA8C2C","E626CC3902D9258B","CB33E14BF8BA5F5D","E2208F0428016E93","E3D7F3D7C1766196","5CCDF8EBC2245C13","5570D5180470B4DC","1E83EC95CA79039A","31C8739CF37DBEAC","3133351710307B6D","32285AC5B97EE2F6","1A8FBECEAE21B7D8","9D82B5AC2AAFCBCB","89BEC25E3F87DE1E","6449992748F49D99"]
#@NoUseNewKeyEx return 1 #���û��ʹ��������ܣ�ֱ�ӷ���1
#@NoSupNewKeyEx return 2 #��������֧��������ܣ�ֱ�ӷ���2
    myrnd=random.randint(1, (500 -1 ))
    mylen = len(EncInString[myrnd])+1
    if  mylen < 8 :
        mylen = 8 
    outstring = create_string_buffer((mylen* 2+1))#//ע�⣬����Ҫ??һ�����ȣ����ڴ������ѧ����
    for n in range(0,255):
         ret=FindPort(n,DevicePath)
         if ret!=0:
             CloseDongle(DevicePath)
             return ret
         ret=EncString_New(EncInString[myrnd].encode('utf-8'), outstring,DevicePath)
         if outstring.value.decode('utf-8').lower()==EncOutString[myrnd].lower():
             CloseDongle(DevicePath)
             return 0
    CloseDongle(DevicePath)
    return -92


#ʹ����ǿ�㷨һ�����������������������Ч�ط�ֹ����
def CheckKeyByEncstring():
#�Ƽ����ܷ�����������������������������㣬ͬʱ�ڳ����ж�ʹ�ô�����ͬ���ļ������㣬Ȼ����бȽ��жϡ�
    DevicePath=create_string_buffer(260)
#@NoUseKeyEx return 1 #���û��ʹ��������ܣ�ֱ�ӷ���1
    InString=('%X%X' % ((int)(random.uniform(0, 24174836)),(int)(random.uniform(0, 24174836))))
    for n in range(0,255):
         ret=FindPort(n,DevicePath)
         if ret!=0 :
            CloseDongle(DevicePath)
            return ret
         if Sub_CheckKeyByEncstring(InString,DevicePath)==0:
            CloseDongle(DevicePath)
            return 0
    return -92

def Sub_CheckKeyByEncstring(InString,DevicePath):
#//'ʹ����ǿ�㷨���ַ������м���
    nlen = len(InString) + 1
    if nlen < 8 :
          nlen = 8
    outstring = create_string_buffer((nlen * 2+1))#//ע�⣬����Ҫ��1һ�����ȣ����ڴ������ѧ��??
    outstring = StrEnc(InString, '21886E7BB2A9DE91CC6CEBF80B2E6F4A'.encode('utf-8'))
    outstring_2 = create_string_buffer((nlen * 2+1))#//ע�⣬����Ҫ��1һ�����ȣ����ڴ������ѧ��??
    EncString(InString.encode('utf-8'),outstring_2,DevicePath)
    if outstring.lower()==outstring_2.value.decode('utf-8').lower():#//�ȽϽ���Ƿ����
         ret=0
    else:
         ret=-92
    return ret

#//ʹ�ô����ȵķ�����ָ���ĵ�ַ��ȡ�ַ���
def ReadStringEx(addr,DevicePath):
    InArray=c_ubyte*1
    blen = InArray(0)
#//�ȴӵ�ַ0������ǰд����ַ����ĳ�??
    ret = YReadEx(blen, addr, 1, '3CB194E8'.encode('utf-8'), '70D9D4C2'.encode('utf-8'), DevicePath)
    if ret != 0 :
        return ''
    outstring=create_string_buffer(blen[0])		
#�ٴӵ�ַ1��ȡָ�����ȵ��ַ���
    ret = YReadString(outstring, addr+1, blen[0], '3CB194E8'.encode('utf-8'), '70D9D4C2'.encode('utf-8'), DevicePath)
    if ret!=0:
        return ''
    return outstring.value


#//ʹ�ôӴ�������ȡ��Ӧ���ݵķ�ʽ����Ƿ����ָ���ļ�����
def CheckKeyByReadEprom():
    DevicePath=create_string_buffer(260)
#@NoUseCode_data return 1 #���û��ʹ��������ܣ�ֱ�ӷ���1
    for n in range(0,255):
        ret=FindPort(n,DevicePath)
        if ret!=0 :
            CloseDongle(DevicePath)
            return ret
        outstring=ReadStringEx(0,DevicePath)
        if(outstring=='YiLian'.encode('utf-8')):
            CloseDongle(DevicePath)
            return 0
    CloseDongle(DevicePath)
    return -92

#ʹ����ͨ�㷨һ����ָ���ļ�����
def CheckKeyByFindort_2():
    DevicePath=create_string_buffer(260)
    ret=FindPort_2(0, 1, **********, DevicePath)
    CloseDongle(DevicePath)
    return ret

def CloseDongle(DevicePath):
    if 'Linux' in platform.system():
        CloseUsbHandle(DevicePath)#�ر�USB�豸


import sys
from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QAction, QImageReader

from utils.constants import APP_NAME, APP_VERSION
from ui.main_window import MainWindow

def verify_license():
    """验证加密锁授权"""
    ret = CheckKeyByEncstring_New()
    
    if ret != 0:
        app = QApplication(sys.argv)
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("授权验证失败")
        
        if ret == -92:
            error_msg = "未检测到有效的加密锁，请插入授权加密锁后重试。"
        elif ret == 1:
            error_msg = "加密验证模块未启用。"
        elif ret == 2:
            error_msg = "该加密锁不支持此功能。"
        else:
            error_msg = f"加密锁验证失败，错误代码: {ret}"
        
        msg.setText(error_msg)
        msg.exec()
        sys.exit(1)
    
    return True


def main():
    """主函数"""
    #verify_license()
    app = QApplication(sys.argv)
    
    # 设置QImageReader内存限制
    QImageReader.setAllocationLimit(2048)
    
    # 设置应用属性
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setOrganizationName("IntelliAnnotate Inc.")
    
    # 设置样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main()) 