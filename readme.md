# IntelliAnnotate - 智能机械图纸标注工具 (集成EasyOCR)

一个专为机械制造业设计的智能图纸标注应用，使用EasyOCR进行真实的文字识别，特别优化了对紧固件图纸的识别效果。

## 🌟 主要特性

### 核心功能
- **真实OCR识别**: 使用EasyOCR进行文字识别，支持中文和英文
- **多格式支持**: PNG、JPG、PDF、DXF格式图纸
- **智能预处理**: 针对机械图纸优化的图像预处理算法
- **文本分类**: 自动识别螺纹规格、直径标注、尺寸标注等机械图纸特有元素
- **可交互标注**: 气泡式标注，支持拖拽、编辑和样式切换
- **实时编辑**: 标注属性实时编辑功能

### OCR识别特性
- **高精度识别**: 针对机械图纸文字特点优化
- **多语言支持**: 中文、英文、数字混合识别
- **置信度筛选**: 可调节置信度阈值过滤低质量结果
- **文本类型分类**: 
  - 螺纹规格 (M8, M10等)
  - 直径标注 (Φ20等)
  - 尺寸标注 (长×宽格式)
  - 角度标注 (含°符号)
  - 材料标记
  - 表面处理标注

### 标注功能
- **多种样式**: 默认、警告、错误、成功四种视觉样式
- **批量创建**: 从OCR结果一键创建所有标注
- **智能筛选**: 按文本类型筛选OCR结果
- **区域选择**: 框选区域创建标注
- **右键菜单**: 便捷的上下文操作

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 推荐使用GPU加速（CUDA支持）

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动应用
```bash
python run.py
```

## 📖 使用指南

### 1. 加载图纸
- 点击菜单 `文件 → 打开` 或工具栏的打开按钮
- 支持的格式：PNG、JPG、JPEG、PDF、DXF
- 加载后图纸会显示在主视图中

### 2. OCR识别设置
在左侧OCR控制面板中：
- **语言选择**: 根据图纸选择识别语言
- **置信度阈值**: 调节识别结果的质量要求（0.1-0.9）
- **GPU加速**: 如果有CUDA显卡会自动启用
- **预处理选项**: 
  - 增强对比度：提高文字清晰度
  - 降噪处理：减少噪点干扰
  - 二值化处理：转换为黑白图像

### 3. 开始识别
1. 点击 `🔍 开始OCR识别` 按钮
2. 等待识别完成（显示进度条）
3. 识别结果会以彩色边界框显示在图纸上
4. 不同类型的文本用不同颜色标识：
   - 🔴 红色：螺纹规格
   - 🟢 绿色：直径标注
   - 🔵 蓝色：尺寸标注
   - 🟡 黄色：角度标注
   - 🟣 紫色：数值
   - 🟦 青色：材料标记
   - 🟠 橙色：表面处理

### 4. 创建标注
识别完成后可以：
- **全部标注**: 从所有OCR结果创建气泡标注
- **标注选中**: 仅对特定结果创建标注
- **筛选结果**: 按文本类型筛选显示
- **清除OCR**: 清除所有OCR结果

### 5. 编辑标注
- **选择标注**: 点击气泡标注进行选择
- **移动标注**: 拖拽气泡标注到新位置
- **编辑文本**: 在右侧属性面板修改标注文本
- **更改样式**: 右键点击标注选择新样式
- **删除标注**: 右键菜单选择删除

### 6. 视图操作
- **缩放**: 鼠标滚轮缩放图纸
- **平移**: 按住鼠标左键拖拽平移视图
- **适应窗口**: 工具栏适应窗口按钮
- **区域选择**: 启用后可框选区域创建标注

## 🔧 技术特点

### OCR优化算法
1. **自适应阈值化**: 适应不同光照条件
2. **CLAHE对比度增强**: 改善局部对比度
3. **双边滤波降噪**: 保持边缘的同时去噪
4. **形态学操作**: 连接断开的文字笔画

### 机械图纸文本识别
- 支持标准机械制图符号：Φ、∅、°、×等
- 螺纹规格自动识别：M6、M8、M10等
- 公差标注处理：±符号识别
- 表面粗糙度符号识别

### 性能优化
- 多线程OCR处理，不阻塞UI
- GPU加速（如果可用）
- 内存优化的图像处理
- 智能缓存机制

## 🎯 适用场景

### 机械制造业
- 紧固件图纸标注
- 零件加工图纸审查
- 技术文档数字化
- 质量检验标注

### 工程设计
- CAD图纸审查
- 设计变更标注
- 技术交流协作
- 设计规范检查

## ⚙️ 配置说明

### GPU加速设置
如果系统安装了CUDA：
- 应用会自动检测并启用GPU加速
- 可在OCR设置中手动开启/关闭
- GPU加速可显著提升识别速度

### 识别参数调优
- **置信度阈值**: 0.3-0.7适合大多数情况
- **语言组合**: 中文+英文适合中文图纸
- **预处理选项**: 建议全部启用以获得最佳效果

## 🐛 故障排除

### 常见问题
1. **OCR识别失败**
   - 检查图纸清晰度
   - 调整预处理选项
   - 降低置信度阈值

2. **识别结果不准确**
   - 尝试不同的语言设置
   - 调整图像预处理参数
   - 检查图纸质量

3. **应用启动失败**
   - 确认Python版本3.8+
   - 重新安装依赖包
   - 检查CUDA环境（可选）

### 性能优化建议
- 使用高分辨率图纸获得更好效果
- 确保图纸对比度良好
- 避免图纸倾斜或变形
- 使用GPU加速提升速度

## 📝 开发说明

### 项目结构
```
├── intelliannotate.py    # 主程序文件
├── run.py               # 启动脚本
├── requirements.txt     # 依赖包列表
├── build.py            # 打包脚本
└── assets/             # 资源文件
```

### 核心类说明
- `OCRWorker`: OCR识别工作线程
- `BubbleAnnotationItem`: 气泡标注图形项
- `GraphicsView`: 自定义图形视图
- `MainWindow`: 主窗口类

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**IntelliAnnotate** - 让机械图纸标注更智能、更高效！