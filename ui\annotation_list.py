# ui/annotation_list.py

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from PySide6.QtWidgets import QTableWidget, QTableWidgetItem, QAbstractItemView, QHeaderView
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QFont

# 导入我们的数据模型类
from core.annotation_item import BubbleAnnotationItem
from utils.constants import GDT_SYMBOL_MAP, GDT_TEXT_MAP

class AnnotationTable(QTableWidget):
    """
    一个用于显示结构化标注数据的表格视图。
    - 调整了审核状态列的宽度
    - 显示审核状态的勾号
    - 支持GD&T符号显示
    """
    annotation_selected = Signal(int)

    def __init__(self):
        super().__init__()
        self.setColumnCount(6)
        self.setHorizontalHeaderLabels(["序号", "类型", "尺寸", "上公差", "下公差", "审核"]) # Header文字改短
        
        self.setup_style()
        self.setup_behavior()

        self.itemClicked.connect(self._on_item_clicked)

    def setup_style(self):
        self.verticalHeader().setVisible(False)
        self.setAlternatingRowColors(True)
        self.setShowGrid(True)
        
        header = self.horizontalHeader()
        # --- 核心修改：调整列宽 ---
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        # 将最后一列（审核状态）设置为固定宽度，使其变窄
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed) 
        self.setColumnWidth(5, 50) # 设置一个合适的宽度，例如50像素
        # ---------------------------
        
        self.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff; /* 主背景色改为纯白 */
                border: 1px solid #d3d3d3; /* 更柔和的边框 */
                border-radius: 4px;
                font-size: 13px; /* 字体稍大一点 */
                gridline-color: #e8e8e8; /* 网格线颜色 */
                color: #333333; /* 默认字体颜色 */
            }
            QHeaderView::section {
                background-color: #4a69bd; /* 统一、清晰的蓝色表头 */
                padding: 6px;
                border: none; /* 移除表头边框 */
                border-bottom: 1px solid #d3d3d3;
                font-weight: bold;
                color: white; /* 表头文字改为白色 */
            }
            QTableWidget::item {
                padding-left: 8px; /* 增加内边距 */
                border: none; /* 移除单元格边框 */
                border-bottom: 1px solid #e8e8e8; /* 只保留底部边框 */
            }
            QTableWidget::item:selected {
                background-color: #0078d7; /* 清晰的蓝色选中背景 */
                color: #ffffff; /* 选中时文字为白色 */
            }
            QTableWidget {
                alternate-background-color: #f5faff; /* 淡蓝色交替行背景 */
            }
        """)

    def setup_behavior(self):
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

    def add_annotation(self, annotation: BubbleAnnotationItem, parsed_data: dict):
        row_position = self.rowCount()
        self.insertRow(row_position)

        id_item = QTableWidgetItem(str(annotation.annotation_id))
        id_item.setData(Qt.ItemDataRole.UserRole, annotation.annotation_id)
        self.setItem(row_position, 0, id_item)
        
        # 使用GD&T符号映射表转换显示
        dim_type = annotation.dimension_type
        symbol = GDT_SYMBOL_MAP.get(dim_type, dim_type)  # 如果找不到对应符号，就使用原文本
        
        type_item = QTableWidgetItem(symbol)
        # 设置更大的字体，使符号更清晰
        font = QFont("Arial", 14, QFont.Bold)
        type_item.setFont(font)
        type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)  # 居中对齐
        self.setItem(row_position, 1, type_item)
        
        self.setItem(row_position, 2, QTableWidgetItem(annotation.dimension))
        self.setItem(row_position, 3, QTableWidgetItem(annotation.upper_tolerance))
        self.setItem(row_position, 4, QTableWidgetItem(annotation.lower_tolerance))
        
        # --- 新增：处理审核状态列 ---
        audit_text = "✅" if annotation.is_audited else ""
        audit_item = QTableWidgetItem(audit_text)
        audit_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter) # 居中显示勾号
        self.setItem(row_position, 5, audit_item)
        # ---------------------------

        tooltip_text = f"标注 {annotation.annotation_id}\n"
        if dim_type != symbol:  # 如果使用了符号替换，在提示中显示原文本
            tooltip_text += f"类型: {GDT_TEXT_MAP.get(symbol, dim_type)}\n"
        tooltip_text += f"原始文本: {annotation.text}"
        
        for col in range(self.columnCount()):
            self.item(row_position, col).setToolTip(tooltip_text)

    def clear_annotations(self):
        self.setRowCount(0)

    def sort_annotations(self, annotations):
        """按照标注ID排序，然后批量添加到表格
        
        Args:
            annotations: 要排序并添加的标注列表
        """
        # 按照标注ID进行排序
        sorted_annotations = sorted(annotations, key=lambda ann: ann.annotation_id)
        
        # 清空现有表格
        self.clear_annotations()
        
        # 按顺序添加排序后的标注
        for annotation in sorted_annotations:
            self.add_annotation(annotation, {})

    def _on_item_clicked(self, item: QTableWidgetItem):
        id_item = self.item(item.row(), 0)
        if id_item:
            annotation_id = id_item.data(Qt.ItemDataRole.UserRole)
            if annotation_id is not None:
                self.annotation_selected.emit(annotation_id)

    def highlight_annotation(self, annotation_id: int):
        for row in range(self.rowCount()):
            item = self.item(row, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == annotation_id:
                self.selectRow(row)
                break

    def update_annotation_data(self, annotation: BubbleAnnotationItem):
        for row in range(self.rowCount()):
            item = self.item(row, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == annotation.annotation_id:
                # 使用GD&T符号映射表转换显示
                dim_type = annotation.dimension_type
                symbol = GDT_SYMBOL_MAP.get(dim_type, dim_type)  # 如果找不到对应符号，就使用原文本
                
                type_item = self.item(row, 1)
                type_item.setText(symbol)
                # 设置更大的字体，使符号更清晰
                font = QFont("Arial", 14, QFont.Bold)
                type_item.setFont(font)
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)  # 居中对齐
                
                self.item(row, 2).setText(annotation.dimension)
                self.item(row, 3).setText(annotation.upper_tolerance)
                self.item(row, 4).setText(annotation.lower_tolerance)
                
                # --- 新增：更新审核状态列 ---
                audit_text = "✅" if annotation.is_audited else ""
                self.item(row, 5).setText(audit_text)
                self.item(row, 5).setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # ---------------------------
                
                tooltip_text = f"标注 {annotation.annotation_id}\n"
                if dim_type != symbol:  # 如果使用了符号替换，在提示中显示原文本
                    tooltip_text += f"类型: {GDT_TEXT_MAP.get(symbol, dim_type)}\n"
                tooltip_text += f"原始文本: {annotation.text}"
                
                for col in range(self.columnCount()):
                    self.item(row, col).setToolTip(tooltip_text)
                    
                break   